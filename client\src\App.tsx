import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';

// Layout Components
import PublicLayout from './components/layouts/PublicLayout';
import DashboardLayout from './components/layouts/DashboardLayout';

// Public Pages
import HomePage from './pages/public/HomePage';
import VehiclesPage from './pages/public/VehiclesPage';
import VehicleDetailPage from './pages/public/VehicleDetailPage';
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import ForgotPasswordPage from './pages/auth/ForgotPasswordPage';

// Dashboard Pages
import DashboardOverview from './pages/dashboard/DashboardOverview';
import VehicleManagement from './pages/dashboard/VehicleManagement';
import BookingManagement from './pages/dashboard/BookingManagement';
import CustomerManagement from './pages/dashboard/CustomerManagement';
import CompanyManagement from './pages/dashboard/CompanyManagement';
import UserManagement from './pages/dashboard/UserManagement';
import ReportsPage from './pages/dashboard/ReportsPage';
import SettingsPage from './pages/dashboard/SettingsPage';
import ProfilePage from './pages/dashboard/ProfilePage';

// Customer Pages
import CustomerDashboard from './pages/customer/CustomerDashboard';
import MyBookings from './pages/customer/MyBookings';
import BookingDetail from './pages/customer/BookingDetail';

// Components
import LoadingSpinner from './components/common/LoadingSpinner';
import ProtectedRoute from './components/auth/ProtectedRoute';

function App() {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/" element={<PublicLayout />}>
        <Route index element={<HomePage />} />
        <Route path="vehicles" element={<VehiclesPage />} />
        <Route path="vehicles/:id" element={<VehicleDetailPage />} />
        <Route path="login" element={<LoginPage />} />
        <Route path="register" element={<RegisterPage />} />
        <Route path="forgot-password" element={<ForgotPasswordPage />} />
      </Route>

      {/* Dashboard Routes */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <DashboardLayout />
          </ProtectedRoute>
        }
      >
        {/* Super Admin Routes */}
        <Route
          index
          element={
            user?.role === 'super_admin' ? (
              <DashboardOverview />
            ) : user?.role === 'customer' ? (
              <CustomerDashboard />
            ) : (
              <DashboardOverview />
            )
          }
        />
        
        {/* Company Management Routes */}
        <Route
          path="companies"
          element={
            <ProtectedRoute allowedRoles={['super_admin']}>
              <CompanyManagement />
            </ProtectedRoute>
          }
        />
        
        {/* Vehicle Management Routes */}
        <Route
          path="vehicles"
          element={
            <ProtectedRoute allowedRoles={['super_admin', 'company_admin', 'employee']}>
              <VehicleManagement />
            </ProtectedRoute>
          }
        />
        
        {/* Booking Management Routes */}
        <Route
          path="bookings"
          element={
            <ProtectedRoute allowedRoles={['super_admin', 'company_admin', 'employee']}>
              <BookingManagement />
            </ProtectedRoute>
          }
        />
        
        {/* Customer Management Routes */}
        <Route
          path="customers"
          element={
            <ProtectedRoute allowedRoles={['super_admin', 'company_admin', 'employee']}>
              <CustomerManagement />
            </ProtectedRoute>
          }
        />
        
        {/* User Management Routes */}
        <Route
          path="users"
          element={
            <ProtectedRoute allowedRoles={['super_admin', 'company_admin']}>
              <UserManagement />
            </ProtectedRoute>
          }
        />
        
        {/* Reports Routes */}
        <Route
          path="reports"
          element={
            <ProtectedRoute allowedRoles={['super_admin', 'company_admin']}>
              <ReportsPage />
            </ProtectedRoute>
          }
        />
        
        {/* Customer-specific Routes */}
        <Route
          path="my-bookings"
          element={
            <ProtectedRoute allowedRoles={['customer']}>
              <MyBookings />
            </ProtectedRoute>
          }
        />
        
        <Route
          path="my-bookings/:id"
          element={
            <ProtectedRoute allowedRoles={['customer']}>
              <BookingDetail />
            </ProtectedRoute>
          }
        />
        
        {/* Common Routes */}
        <Route path="profile" element={<ProfilePage />} />
        <Route path="settings" element={<SettingsPage />} />
      </Route>

      {/* Redirect to appropriate dashboard based on role */}
      <Route
        path="/app"
        element={
          user ? (
            <Navigate to="/dashboard" replace />
          ) : (
            <Navigate to="/login" replace />
          )
        }
      />

      {/* Catch all route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}

export default App;
