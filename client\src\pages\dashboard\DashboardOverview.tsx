import React from 'react';
import { useQuery } from 'react-query';
import {
  TruckIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UsersIcon,
  BuildingOfficeIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import { dashboardAPI, mockAPI } from '../../services/api';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import StatsCard from '../../components/dashboard/StatsCard';
import RecentBookings from '../../components/dashboard/RecentBookings';
import RevenueChart from '../../components/dashboard/RevenueChart';
import VehicleUtilization from '../../components/dashboard/VehicleUtilization';

export default function DashboardOverview() {
  const { user } = useAuth();

  // Fetch dashboard data
  const { data: dashboardData, isLoading, error } = useQuery(
    'dashboard-overview',
    async () => {
      try {
        // Try real API first, fallback to mock data
        const response = await dashboardAPI.getOverview();
        return response.data.data;
      } catch (error) {
        // Fallback to mock data for development
        const mockResponse = await mockAPI.getDashboard();
        return mockResponse.data.data;
      }
    },
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">
          <ChartBarIcon className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load dashboard</h3>
        <p className="text-gray-500">Please try refreshing the page</p>
      </div>
    );
  }

  const stats = dashboardData || {};

  // Render different dashboard based on user role
  const renderSuperAdminDashboard = () => (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Companies"
          value={stats.companies?.total || 0}
          icon={BuildingOfficeIcon}
          color="blue"
          change={stats.companies?.active ? `${stats.companies.active} active` : undefined}
        />
        <StatsCard
          title="Total Vehicles"
          value={stats.vehicles?.total || 0}
          icon={TruckIcon}
          color="green"
          change={`${stats.vehicles?.utilization || 0}% utilization`}
        />
        <StatsCard
          title="Total Bookings"
          value={stats.bookings?.total || 0}
          icon={CalendarIcon}
          color="purple"
          change={`${stats.bookings?.active || 0} active`}
        />
        <StatsCard
          title="Total Revenue"
          value={`$${(stats.revenue?.total || 0).toLocaleString()}`}
          icon={CurrencyDollarIcon}
          color="yellow"
          change="This month"
        />
      </div>

      {/* Charts and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RevenueChart />
        <VehicleUtilization />
      </div>

      <RecentBookings bookings={stats.recentBookings || []} />
    </div>
  );

  const renderCompanyDashboard = () => (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Fleet Size"
          value={stats.vehicles?.total || 0}
          icon={TruckIcon}
          color="blue"
          change={`${stats.vehicles?.available || 0} available`}
        />
        <StatsCard
          title="Active Bookings"
          value={stats.bookings?.active || 0}
          icon={CalendarIcon}
          color="green"
          change={`${stats.bookings?.pending || 0} pending`}
        />
        <StatsCard
          title="Monthly Revenue"
          value={`$${(stats.revenue?.monthly || 0).toLocaleString()}`}
          icon={CurrencyDollarIcon}
          color="purple"
          change={`$${(stats.revenue?.averageBooking || 0).toFixed(0)} avg booking`}
        />
        <StatsCard
          title="Utilization"
          value={`${stats.vehicles?.utilization || 0}%`}
          icon={ChartBarIcon}
          color="yellow"
          change={`${stats.vehicles?.rented || 0} vehicles rented`}
        />
      </div>

      {/* Vehicle Status */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <RevenueChart />
        </div>
        <div>
          <VehicleUtilization />
        </div>
      </div>

      <RecentBookings bookings={stats.recentBookings || []} />
    </div>
  );

  const renderCustomerDashboard = () => (
    <div className="space-y-6">
      {/* Welcome Message */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome back, {user?.firstName}!
        </h1>
        <p className="text-blue-100">
          Manage your bookings and explore our vehicle fleet.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard
          title="Total Bookings"
          value={stats.bookings?.total || 0}
          icon={CalendarIcon}
          color="blue"
          change={`${stats.bookings?.completed || 0} completed`}
        />
        <StatsCard
          title="Active Bookings"
          value={stats.bookings?.active || 0}
          icon={TruckIcon}
          color="green"
          change="Currently rented"
        />
        <StatsCard
          title="Total Spent"
          value={`$${(stats.spending?.total || 0).toLocaleString()}`}
          icon={CurrencyDollarIcon}
          color="purple"
          change={`$${(stats.spending?.average || 0).toFixed(0)} average`}
        />
      </div>

      {/* Recent Bookings */}
      <RecentBookings 
        bookings={stats.recentBookings || []} 
        title="Your Recent Bookings"
        showCustomer={false}
      />

      {/* Favorite Vehicles */}
      {stats.favoriteVehicles && stats.favoriteVehicles.length > 0 && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Your Favorite Vehicles</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {stats.favoriteVehicles.map((item: any, index: number) => (
              <div key={index} className="border rounded-lg p-4">
                <h4 className="font-medium text-gray-900">
                  {item.vehicle.make} {item.vehicle.model}
                </h4>
                <p className="text-sm text-gray-500">
                  {item.bookings} booking{item.bookings !== 1 ? 's' : ''}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  // Render appropriate dashboard based on user role
  const renderDashboard = () => {
    switch (user?.role) {
      case 'super_admin':
        return renderSuperAdminDashboard();
      case 'company_admin':
      case 'employee':
        return renderCompanyDashboard();
      case 'customer':
        return renderCustomerDashboard();
      default:
        return renderCompanyDashboard();
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Dashboard
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            {user?.role === 'customer' 
              ? 'Manage your bookings and account'
              : 'Overview of your business performance'
            }
          </p>
        </div>
        <div className="mt-4 flex md:ml-4 md:mt-0">
          <button className="btn-primary">
            {user?.role === 'customer' ? 'Browse Vehicles' : 'Quick Actions'}
          </button>
        </div>
      </div>

      {/* Dashboard Content */}
      {renderDashboard()}
    </div>
  );
}
