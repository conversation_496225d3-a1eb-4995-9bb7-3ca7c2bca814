const express = require('express');
const { body, validationResult } = require('express-validator');
const Vehicle = require('../models/Vehicle');
const Company = require('../models/Company');
const Booking = require('../models/Booking');
const { protect, authorize, checkCompanyAccess, checkResourceAccess } = require('../middleware/auth');

const router = express.Router();

// @desc    Get all vehicles
// @route   GET /api/vehicles
// @access  Public (for browsing) / Private (for management)
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    // Build query
    let query = { isActive: true };
    
    // Company filter
    if (req.query.company) {
      query.company = req.query.company;
    }
    
    // Category filter
    if (req.query.category) {
      query.category = req.query.category;
    }
    
    // Type filter
    if (req.query.type) {
      query.type = req.query.type;
    }
    
    // Status filter
    if (req.query.status) {
      query.status = req.query.status;
    } else {
      // Default to available for public browsing
      if (!req.headers.authorization) {
        query.status = 'available';
      }
    }
    
    // Price range filter
    if (req.query.minPrice || req.query.maxPrice) {
      query['pricing.daily'] = {};
      if (req.query.minPrice) {
        query['pricing.daily'].$gte = parseFloat(req.query.minPrice);
      }
      if (req.query.maxPrice) {
        query['pricing.daily'].$lte = parseFloat(req.query.maxPrice);
      }
    }
    
    // Seating capacity filter
    if (req.query.minSeats) {
      query['specifications.seatingCapacity'] = { $gte: parseInt(req.query.minSeats) };
    }
    
    // Features filter
    if (req.query.features) {
      const features = req.query.features.split(',');
      query.features = { $in: features };
    }
    
    // Search filter
    if (req.query.search) {
      query.$or = [
        { make: { $regex: req.query.search, $options: 'i' } },
        { model: { $regex: req.query.search, $options: 'i' } },
        { licensePlate: { $regex: req.query.search, $options: 'i' } }
      ];
    }

    // Sort options
    let sort = {};
    if (req.query.sortBy) {
      switch (req.query.sortBy) {
        case 'price_low':
          sort = { 'pricing.daily': 1 };
          break;
        case 'price_high':
          sort = { 'pricing.daily': -1 };
          break;
        case 'year_new':
          sort = { year: -1 };
          break;
        case 'year_old':
          sort = { year: 1 };
          break;
        case 'rating':
          sort = { averageRating: -1 };
          break;
        default:
          sort = { createdAt: -1 };
      }
    } else {
      sort = { createdAt: -1 };
    }

    const vehicles = await Vehicle.find(query)
      .populate('company', 'name slug')
      .populate('createdBy', 'firstName lastName')
      .sort(sort)
      .skip(skip)
      .limit(limit);

    const total = await Vehicle.countDocuments(query);

    // Get filter options for frontend
    const filterOptions = await Vehicle.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: null,
          categories: { $addToSet: '$category' },
          types: { $addToSet: '$type' },
          makes: { $addToSet: '$make' },
          minPrice: { $min: '$pricing.daily' },
          maxPrice: { $max: '$pricing.daily' },
          features: { $addToSet: '$features' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        vehicles,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        filters: filterOptions[0] || {}
      }
    });

  } catch (error) {
    console.error('Get vehicles error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get single vehicle
// @route   GET /api/vehicles/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const vehicle = await Vehicle.findById(req.params.id)
      .populate('company', 'name slug contactInfo branches')
      .populate('createdBy', 'firstName lastName');

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    // Get availability for next 30 days
    const startDate = new Date();
    const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    
    const bookings = await Booking.find({
      vehicle: req.params.id,
      status: { $in: ['confirmed', 'active'] },
      $or: [
        {
          pickupDateTime: { $lte: endDate },
          dropoffDateTime: { $gte: startDate }
        }
      ]
    }).select('pickupDateTime dropoffDateTime status');

    // Get similar vehicles
    const similarVehicles = await Vehicle.find({
      _id: { $ne: req.params.id },
      category: vehicle.category,
      company: vehicle.company._id,
      status: 'available',
      isActive: true
    }).limit(4).populate('company', 'name');

    res.status(200).json({
      success: true,
      data: {
        vehicle,
        bookings,
        similarVehicles
      }
    });

  } catch (error) {
    console.error('Get vehicle error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Create new vehicle
// @route   POST /api/vehicles
// @access  Private (Company Admin, Employee, Super Admin)
router.post('/', protect, authorize('company_admin', 'employee', 'super_admin'), checkCompanyAccess, [
  body('make').trim().isLength({ min: 2 }).withMessage('Vehicle make is required'),
  body('model').trim().isLength({ min: 2 }).withMessage('Vehicle model is required'),
  body('year').isInt({ min: 1990, max: new Date().getFullYear() + 1 }).withMessage('Invalid year'),
  body('color').notEmpty().withMessage('Vehicle color is required'),
  body('licensePlate').notEmpty().withMessage('License plate is required'),
  body('vin').isLength({ min: 17, max: 17 }).withMessage('VIN must be 17 characters'),
  body('category').isIn(['economy', 'compact', 'midsize', 'fullsize', 'luxury', 'suv', 'van', 'truck']).withMessage('Invalid category'),
  body('type').isIn(['sedan', 'hatchback', 'coupe', 'convertible', 'suv', 'pickup', 'van', 'minivan']).withMessage('Invalid type'),
  body('pricing.daily').isFloat({ min: 0 }).withMessage('Daily price must be a positive number'),
  body('pricing.deposit').isFloat({ min: 0 }).withMessage('Deposit must be a positive number')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Check if license plate or VIN already exists
    const existingVehicle = await Vehicle.findOne({
      $or: [
        { licensePlate: req.body.licensePlate.toUpperCase() },
        { vin: req.body.vin.toUpperCase() }
      ]
    });

    if (existingVehicle) {
      return res.status(400).json({
        success: false,
        message: 'Vehicle with this license plate or VIN already exists'
      });
    }

    // Set company based on user role
    let companyId;
    if (req.user.role === 'super_admin' && req.body.company) {
      companyId = req.body.company;
    } else {
      companyId = req.user.company._id;
    }

    // Check subscription limits
    const company = await Company.findById(companyId);
    const vehicleCount = await Vehicle.countDocuments({ company: companyId, isActive: true });
    
    if (vehicleCount >= company.subscription.maxVehicles) {
      return res.status(400).json({
        success: false,
        message: `Vehicle limit reached. Current plan allows ${company.subscription.maxVehicles} vehicles.`
      });
    }

    const vehicleData = {
      ...req.body,
      company: companyId,
      createdBy: req.user._id,
      licensePlate: req.body.licensePlate.toUpperCase(),
      vin: req.body.vin.toUpperCase()
    };

    const vehicle = await Vehicle.create(vehicleData);

    const populatedVehicle = await Vehicle.findById(vehicle._id)
      .populate('company', 'name')
      .populate('createdBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      message: 'Vehicle created successfully',
      data: populatedVehicle
    });

  } catch (error) {
    console.error('Create vehicle error:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Vehicle with this license plate or VIN already exists'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error during vehicle creation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Update vehicle
// @route   PUT /api/vehicles/:id
// @access  Private (Company Admin, Employee, Super Admin)
router.put('/:id', protect, authorize('company_admin', 'employee', 'super_admin'), checkResourceAccess('vehicle'), async (req, res) => {
  try {
    const vehicle = await Vehicle.findById(req.params.id);

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    // Define allowed fields
    const allowedFields = [
      'make', 'model', 'year', 'color', 'category', 'type', 'specifications',
      'features', 'pricing', 'status', 'currentMileage', 'insurance',
      'registration', 'images', 'currentLocation'
    ];

    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    // Handle license plate and VIN updates
    if (req.body.licensePlate) {
      updates.licensePlate = req.body.licensePlate.toUpperCase();
    }
    if (req.body.vin) {
      updates.vin = req.body.vin.toUpperCase();
    }

    const updatedVehicle = await Vehicle.findByIdAndUpdate(
      req.params.id,
      updates,
      {
        new: true,
        runValidators: true
      }
    ).populate('company', 'name').populate('createdBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      message: 'Vehicle updated successfully',
      data: updatedVehicle
    });

  } catch (error) {
    console.error('Update vehicle error:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Vehicle with this license plate or VIN already exists'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error during vehicle update',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Delete vehicle
// @route   DELETE /api/vehicles/:id
// @access  Private (Company Admin, Super Admin)
router.delete('/:id', protect, authorize('company_admin', 'super_admin'), checkResourceAccess('vehicle'), async (req, res) => {
  try {
    const vehicle = await Vehicle.findById(req.params.id);

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    // Check if vehicle has active bookings
    const activeBookings = await Booking.countDocuments({
      vehicle: req.params.id,
      status: { $in: ['confirmed', 'active'] }
    });

    if (activeBookings > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete vehicle with active bookings'
      });
    }

    // Soft delete - just deactivate
    vehicle.isActive = false;
    vehicle.status = 'out_of_service';
    await vehicle.save();

    res.status(200).json({
      success: true,
      message: 'Vehicle deleted successfully'
    });

  } catch (error) {
    console.error('Delete vehicle error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during vehicle deletion',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
