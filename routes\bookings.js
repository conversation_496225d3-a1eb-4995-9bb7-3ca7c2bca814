const express = require('express');
const { body, validationResult } = require('express-validator');
const Booking = require('../models/Booking');
const Vehicle = require('../models/Vehicle');
const User = require('../models/User');
const { protect, authorize, checkResourceAccess } = require('../middleware/auth');

const router = express.Router();

// @desc    Get all bookings
// @route   GET /api/bookings
// @access  Private
router.get('/', protect, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build query based on user role
    let query = {};
    
    if (req.user.role === 'customer') {
      query.customer = req.user._id;
    } else if (req.user.role === 'company_admin' || req.user.role === 'employee') {
      query.company = req.user.company._id;
    }
    
    // Status filter
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    // Date range filter
    if (req.query.startDate || req.query.endDate) {
      query.pickupDateTime = {};
      if (req.query.startDate) {
        query.pickupDateTime.$gte = new Date(req.query.startDate);
      }
      if (req.query.endDate) {
        query.pickupDateTime.$lte = new Date(req.query.endDate);
      }
    }
    
    // Vehicle filter
    if (req.query.vehicle) {
      query.vehicle = req.query.vehicle;
    }
    
    // Customer filter (for company users)
    if (req.query.customer && (req.user.role === 'company_admin' || req.user.role === 'employee' || req.user.role === 'super_admin')) {
      query.customer = req.query.customer;
    }
    
    // Search filter
    if (req.query.search) {
      query.bookingNumber = { $regex: req.query.search, $options: 'i' };
    }

    const bookings = await Booking.find(query)
      .populate('customer', 'firstName lastName email phone')
      .populate('vehicle', 'make model year licensePlate images')
      .populate('company', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Booking.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        bookings,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get bookings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get single booking
// @route   GET /api/bookings/:id
// @access  Private
router.get('/:id', protect, checkResourceAccess('booking'), async (req, res) => {
  try {
    const booking = await Booking.findById(req.params.id)
      .populate('customer', 'firstName lastName email phone address drivingLicense')
      .populate('vehicle', 'make model year licensePlate images specifications pricing')
      .populate('company', 'name contactInfo branches')
      .populate('createdBy', 'firstName lastName')
      .populate('notes.author', 'firstName lastName role');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    res.status(200).json({
      success: true,
      data: booking
    });

  } catch (error) {
    console.error('Get booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Create new booking
// @route   POST /api/bookings
// @access  Private
router.post('/', protect, [
  body('vehicle').isMongoId().withMessage('Valid vehicle ID is required'),
  body('pickupDateTime').isISO8601().withMessage('Valid pickup date and time is required'),
  body('dropoffDateTime').isISO8601().withMessage('Valid dropoff date and time is required'),
  body('pickupLocation.branch').isMongoId().withMessage('Valid pickup branch is required'),
  body('dropoffLocation.branch').isMongoId().withMessage('Valid dropoff branch is required'),
  body('primaryDriver.name').notEmpty().withMessage('Primary driver name is required'),
  body('primaryDriver.license.number').notEmpty().withMessage('Driver license number is required'),
  body('primaryDriver.phone').isMobilePhone().withMessage('Valid phone number is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      vehicle: vehicleId,
      pickupDateTime,
      dropoffDateTime,
      pickupLocation,
      dropoffLocation,
      primaryDriver,
      additionalDrivers,
      addOns,
      specialRequests,
      insurance
    } = req.body;

    // Validate dates
    const pickup = new Date(pickupDateTime);
    const dropoff = new Date(dropoffDateTime);
    const now = new Date();

    if (pickup <= now) {
      return res.status(400).json({
        success: false,
        message: 'Pickup date must be in the future'
      });
    }

    if (dropoff <= pickup) {
      return res.status(400).json({
        success: false,
        message: 'Dropoff date must be after pickup date'
      });
    }

    // Get vehicle details
    const vehicle = await Vehicle.findById(vehicleId).populate('company');
    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    if (vehicle.status !== 'available') {
      return res.status(400).json({
        success: false,
        message: 'Vehicle is not available for booking'
      });
    }

    // Check vehicle availability for the requested period
    const conflictingBookings = await Booking.countDocuments({
      vehicle: vehicleId,
      status: { $in: ['confirmed', 'active'] },
      $or: [
        {
          pickupDateTime: { $lte: dropoff },
          dropoffDateTime: { $gte: pickup }
        }
      ]
    });

    if (conflictingBookings > 0) {
      return res.status(400).json({
        success: false,
        message: 'Vehicle is not available for the selected dates'
      });
    }

    // Calculate duration and pricing
    const durationMs = dropoff - pickup;
    const hours = Math.ceil(durationMs / (1000 * 60 * 60));
    const days = Math.ceil(hours / 24);

    let baseRate, rateType, subtotal;

    // Determine rate type and calculate subtotal
    if (hours <= 24) {
      if (hours <= 4) {
        baseRate = vehicle.pricing.hourly;
        rateType = 'hourly';
        subtotal = baseRate * hours;
      } else {
        baseRate = vehicle.pricing.daily;
        rateType = 'daily';
        subtotal = baseRate;
      }
    } else if (days <= 7) {
      baseRate = vehicle.pricing.daily;
      rateType = 'daily';
      subtotal = baseRate * days;
    } else if (days <= 30 && vehicle.pricing.weekly) {
      const weeks = Math.ceil(days / 7);
      baseRate = vehicle.pricing.weekly;
      rateType = 'weekly';
      subtotal = baseRate * weeks;
    } else if (vehicle.pricing.monthly) {
      const months = Math.ceil(days / 30);
      baseRate = vehicle.pricing.monthly;
      rateType = 'monthly';
      subtotal = baseRate * months;
    } else {
      baseRate = vehicle.pricing.daily;
      rateType = 'daily';
      subtotal = baseRate * days;
    }

    // Calculate additional costs
    let additionalDriversCost = 0;
    if (additionalDrivers && additionalDrivers.length > 0) {
      additionalDriversCost = additionalDrivers.reduce((sum, driver) => sum + (driver.fee || 0), 0);
    }

    let addOnsCost = 0;
    if (addOns && addOns.length > 0) {
      addOnsCost = addOns.reduce((sum, addon) => sum + (addon.cost * addon.quantity), 0);
    }

    let insuranceCost = 0;
    if (insurance && insurance.cost) {
      insuranceCost = insurance.cost;
    }

    // Calculate taxes (example: 10%)
    const taxRate = 0.10;
    const taxes = (subtotal + additionalDriversCost + addOnsCost + insuranceCost) * taxRate;

    const totalAmount = subtotal + additionalDriversCost + addOnsCost + insuranceCost + taxes;

    // Create booking
    const bookingData = {
      company: vehicle.company._id,
      customer: req.user._id,
      vehicle: vehicleId,
      pickupDateTime: pickup,
      dropoffDateTime: dropoff,
      pickupLocation,
      dropoffLocation,
      duration: { hours, days },
      pricing: {
        baseRate,
        rateType,
        subtotal,
        taxes,
        fees: [
          ...(additionalDrivers && additionalDrivers.length > 0 ? [{
            name: 'Additional Drivers',
            amount: additionalDriversCost,
            description: `${additionalDrivers.length} additional driver(s)`
          }] : []),
          ...(addOns && addOns.length > 0 ? addOns.map(addon => ({
            name: addon.name,
            amount: addon.cost * addon.quantity,
            description: addon.description
          })) : [])
        ],
        deposit: vehicle.pricing.deposit,
        totalAmount
      },
      payment: {
        method: req.body.paymentMethod || 'cash',
        remainingAmount: totalAmount
      },
      primaryDriver,
      additionalDrivers: additionalDrivers || [],
      addOns: addOns || [],
      specialRequests,
      insurance: insurance || { type: 'basic', cost: 0 },
      createdBy: req.user._id
    };

    const booking = await Booking.create(bookingData);

    const populatedBooking = await Booking.findById(booking._id)
      .populate('customer', 'firstName lastName email phone')
      .populate('vehicle', 'make model year licensePlate')
      .populate('company', 'name');

    res.status(201).json({
      success: true,
      message: 'Booking created successfully',
      data: populatedBooking
    });

  } catch (error) {
    console.error('Create booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during booking creation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Update booking status
// @route   PUT /api/bookings/:id/status
// @access  Private (Company Admin, Employee, Super Admin)
router.put('/:id/status', protect, authorize('company_admin', 'employee', 'super_admin'), checkResourceAccess('booking'), [
  body('status').isIn(['pending', 'confirmed', 'active', 'completed', 'cancelled', 'no_show']).withMessage('Invalid status')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, reason, vehicleCondition } = req.body;

    const booking = await Booking.findById(req.params.id);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Validate status transitions
    const validTransitions = {
      'pending': ['confirmed', 'cancelled'],
      'confirmed': ['active', 'cancelled', 'no_show'],
      'active': ['completed'],
      'completed': [],
      'cancelled': [],
      'no_show': []
    };

    if (!validTransitions[booking.status].includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Cannot change status from ${booking.status} to ${status}`
      });
    }

    // Update booking status
    booking.status = status;
    booking.lastModifiedBy = req.user._id;

    // Handle specific status changes
    if (status === 'active') {
      booking.actualPickupDateTime = new Date();
      if (vehicleCondition && vehicleCondition.pickup) {
        booking.vehicleCondition.pickup = {
          ...vehicleCondition.pickup,
          inspectedBy: req.user._id,
          inspectionDate: new Date()
        };
      }
      
      // Update vehicle status
      await Vehicle.findByIdAndUpdate(booking.vehicle, { status: 'rented' });
    }

    if (status === 'completed') {
      booking.actualDropoffDateTime = new Date();
      if (vehicleCondition && vehicleCondition.dropoff) {
        booking.vehicleCondition.dropoff = {
          ...vehicleCondition.dropoff,
          inspectedBy: req.user._id,
          inspectionDate: new Date()
        };
      }
      
      // Update vehicle status back to available
      await Vehicle.findByIdAndUpdate(booking.vehicle, { status: 'available' });
    }

    if (status === 'cancelled') {
      booking.cancellation = {
        reason: reason || 'No reason provided',
        cancelledBy: req.user._id,
        cancelledAt: new Date()
      };
      
      // Update vehicle status back to available
      await Vehicle.findByIdAndUpdate(booking.vehicle, { status: 'available' });
    }

    await booking.save();

    const updatedBooking = await Booking.findById(booking._id)
      .populate('customer', 'firstName lastName email phone')
      .populate('vehicle', 'make model year licensePlate')
      .populate('company', 'name');

    res.status(200).json({
      success: true,
      message: 'Booking status updated successfully',
      data: updatedBooking
    });

  } catch (error) {
    console.error('Update booking status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during status update',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Add note to booking
// @route   POST /api/bookings/:id/notes
// @access  Private
router.post('/:id/notes', protect, checkResourceAccess('booking'), [
  body('message').notEmpty().withMessage('Note message is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { message, isInternal } = req.body;

    const booking = await Booking.findById(req.params.id);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Add note
    booking.notes.push({
      author: req.user._id,
      message,
      isInternal: isInternal || false,
      timestamp: new Date()
    });

    await booking.save();

    const updatedBooking = await Booking.findById(booking._id)
      .populate('notes.author', 'firstName lastName role');

    res.status(200).json({
      success: true,
      message: 'Note added successfully',
      data: updatedBooking.notes
    });

  } catch (error) {
    console.error('Add note error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during note addition',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
