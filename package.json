{"name": "autoreserv-saas", "version": "1.0.0", "description": "SaaS platform for car rental companies", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "client": "cd client && npm start", "server": "nodemon server.js", "build": "cd client && npm run build", "test": "jest", "heroku-postbuild": "cd client && npm install && npm run build"}, "keywords": ["car-rental", "saas", "booking", "fleet-management"], "author": "AutoReserv Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5", "cloudinary": "^1.40.0", "stripe": "^13.6.0", "nodemailer": "^6.9.4", "express-validator": "^7.0.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}