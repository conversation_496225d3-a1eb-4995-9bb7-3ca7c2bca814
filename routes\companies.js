const express = require('express');
const { body, validationResult } = require('express-validator');
const Company = require('../models/Company');
const User = require('../models/User');
const Vehicle = require('../models/Vehicle');
const Booking = require('../models/Booking');
const { protect, authorize, checkCompanyAccess } = require('../middleware/auth');

const router = express.Router();

// @desc    Get all companies (Super Admin only)
// @route   GET /api/companies
// @access  Private (Super Admin)
router.get('/', protect, authorize('super_admin'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};
    
    if (req.query.status) {
      query['subscription.status'] = req.query.status;
    }
    
    if (req.query.search) {
      query.$or = [
        { name: { $regex: req.query.search, $options: 'i' } },
        { 'contactInfo.email': { $regex: req.query.search, $options: 'i' } }
      ];
    }

    const companies = await Company.find(query)
      .populate('createdBy', 'firstName lastName email')
      .populate('totalVehicles')
      .populate('totalBookings')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Company.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        companies,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get companies error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get single company
// @route   GET /api/companies/:id
// @access  Private
router.get('/:id', protect, checkCompanyAccess, async (req, res) => {
  try {
    const company = await Company.findById(req.params.id)
      .populate('createdBy', 'firstName lastName email')
      .populate('totalVehicles')
      .populate('totalBookings');

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    res.status(200).json({
      success: true,
      data: company
    });

  } catch (error) {
    console.error('Get company error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Update company
// @route   PUT /api/companies/:id
// @access  Private (Company Admin or Super Admin)
router.put('/:id', protect, authorize('company_admin', 'super_admin'), checkCompanyAccess, [
  body('name').optional().trim().isLength({ min: 2 }).withMessage('Company name must be at least 2 characters'),
  body('contactInfo.email').optional().isEmail().withMessage('Please enter a valid email'),
  body('contactInfo.phone').optional().isMobilePhone().withMessage('Please enter a valid phone number')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const allowedFields = [
      'name', 'description', 'logo', 'contactInfo', 'headquarters',
      'settings', 'businessLicense', 'taxId'
    ];

    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    const company = await Company.findByIdAndUpdate(
      req.params.id,
      updates,
      {
        new: true,
        runValidators: true
      }
    );

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Company updated successfully',
      data: company
    });

  } catch (error) {
    console.error('Update company error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Add branch to company
// @route   POST /api/companies/:id/branches
// @access  Private (Company Admin or Super Admin)
router.post('/:id/branches', protect, authorize('company_admin', 'super_admin'), checkCompanyAccess, [
  body('name').trim().isLength({ min: 2 }).withMessage('Branch name is required'),
  body('address.street').notEmpty().withMessage('Street address is required'),
  body('address.city').notEmpty().withMessage('City is required'),
  body('address.country').notEmpty().withMessage('Country is required'),
  body('phone').isMobilePhone().withMessage('Please enter a valid phone number')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const company = await Company.findById(req.params.id);

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Add new branch
    company.branches.push(req.body);
    await company.save();

    res.status(201).json({
      success: true,
      message: 'Branch added successfully',
      data: company
    });

  } catch (error) {
    console.error('Add branch error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Update branch
// @route   PUT /api/companies/:id/branches/:branchId
// @access  Private (Company Admin or Super Admin)
router.put('/:id/branches/:branchId', protect, authorize('company_admin', 'super_admin'), checkCompanyAccess, async (req, res) => {
  try {
    const company = await Company.findById(req.params.id);

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    const branch = company.branches.id(req.params.branchId);

    if (!branch) {
      return res.status(404).json({
        success: false,
        message: 'Branch not found'
      });
    }

    // Update branch
    Object.keys(req.body).forEach(key => {
      if (req.body[key] !== undefined) {
        branch[key] = req.body[key];
      }
    });

    await company.save();

    res.status(200).json({
      success: true,
      message: 'Branch updated successfully',
      data: company
    });

  } catch (error) {
    console.error('Update branch error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Delete branch
// @route   DELETE /api/companies/:id/branches/:branchId
// @access  Private (Company Admin or Super Admin)
router.delete('/:id/branches/:branchId', protect, authorize('company_admin', 'super_admin'), checkCompanyAccess, async (req, res) => {
  try {
    const company = await Company.findById(req.params.id);

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Check if branch has vehicles
    const vehiclesCount = await Vehicle.countDocuments({
      company: req.params.id,
      branch: req.params.branchId
    });

    if (vehiclesCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete branch with vehicles. Please move or delete vehicles first.'
      });
    }

    // Remove branch
    company.branches.pull(req.params.branchId);
    await company.save();

    res.status(200).json({
      success: true,
      message: 'Branch deleted successfully',
      data: company
    });

  } catch (error) {
    console.error('Delete branch error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get company statistics
// @route   GET /api/companies/:id/stats
// @access  Private (Company Admin, Employee, or Super Admin)
router.get('/:id/stats', protect, authorize('company_admin', 'employee', 'super_admin'), checkCompanyAccess, async (req, res) => {
  try {
    const companyId = req.params.id;

    // Get basic counts
    const [vehiclesCount, bookingsCount, activeBookings, completedBookings] = await Promise.all([
      Vehicle.countDocuments({ company: companyId, isActive: true }),
      Booking.countDocuments({ company: companyId }),
      Booking.countDocuments({ company: companyId, status: 'active' }),
      Booking.countDocuments({ company: companyId, status: 'completed' })
    ]);

    // Get revenue data (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const revenueData = await Booking.aggregate([
      {
        $match: {
          company: new mongoose.Types.ObjectId(companyId),
          status: 'completed',
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$pricing.totalAmount' },
          averageBookingValue: { $avg: '$pricing.totalAmount' }
        }
      }
    ]);

    // Get vehicle utilization
    const vehicleStats = await Vehicle.aggregate([
      { $match: { company: new mongoose.Types.ObjectId(companyId) } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const stats = {
      vehicles: {
        total: vehiclesCount,
        byStatus: vehicleStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {})
      },
      bookings: {
        total: bookingsCount,
        active: activeBookings,
        completed: completedBookings
      },
      revenue: {
        last30Days: revenueData[0]?.totalRevenue || 0,
        averageBookingValue: revenueData[0]?.averageBookingValue || 0
      }
    };

    res.status(200).json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get company stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
