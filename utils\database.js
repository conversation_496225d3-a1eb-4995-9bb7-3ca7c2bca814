const mongoose = require('mongoose');

// Database connection with retry logic
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
    
    // Handle connection events
    mongoose.connection.on('error', (err) => {
      console.error('❌ MongoDB connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
      console.log('⚠️ MongoDB disconnected');
    });

    mongoose.connection.on('reconnected', () => {
      console.log('✅ MongoDB reconnected');
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      console.log('📴 MongoDB connection closed through app termination');
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    
    // Retry connection after 5 seconds
    console.log('🔄 Retrying connection in 5 seconds...');
    setTimeout(connectDB, 5000);
  }
};

// Seed initial data
const seedDatabase = async () => {
  try {
    const User = require('../models/User');
    const Company = require('../models/Company');

    // Check if super admin exists
    const superAdmin = await User.findOne({ role: 'super_admin' });
    
    if (!superAdmin) {
      console.log('🌱 Seeding initial super admin...');
      
      await User.create({
        firstName: 'Super',
        lastName: 'Admin',
        email: process.env.ADMIN_EMAIL || '<EMAIL>',
        password: process.env.ADMIN_PASSWORD || 'admin123456',
        phone: '+**********',
        role: 'super_admin',
        isActive: true,
        isEmailVerified: true
      });

      console.log('✅ Super admin created successfully');
    }

    // Create demo company if none exists
    const companyCount = await Company.countDocuments();
    
    if (companyCount === 0) {
      console.log('🌱 Seeding demo company...');
      
      const demoCompany = await Company.create({
        name: 'Demo Car Rental',
        description: 'Demo car rental company for testing',
        businessLicense: {
          number: 'DEMO123456',
          expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
        },
        taxId: 'TAX123456',
        contactInfo: {
          email: '<EMAIL>',
          phone: '+**********'
        },
        headquarters: {
          street: '123 Demo Street',
          city: 'Demo City',
          country: 'Demo Country'
        },
        branches: [{
          name: 'Main Branch',
          address: {
            street: '123 Demo Street',
            city: 'Demo City',
            country: 'Demo Country'
          },
          phone: '+**********',
          workingHours: {
            monday: { open: '08:00', close: '18:00' },
            tuesday: { open: '08:00', close: '18:00' },
            wednesday: { open: '08:00', close: '18:00' },
            thursday: { open: '08:00', close: '18:00' },
            friday: { open: '08:00', close: '18:00' },
            saturday: { open: '09:00', close: '17:00' },
            sunday: { closed: true }
          }
        }],
        subscription: {
          plan: 'premium',
          status: 'active',
          startDate: new Date(),
          endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
          maxVehicles: 50,
          maxUsers: 20
        },
        isActive: true,
        isVerified: true
      });

      // Create demo company admin
      await User.create({
        firstName: 'Demo',
        lastName: 'Admin',
        email: '<EMAIL>',
        password: 'demo123456',
        phone: '+**********',
        role: 'company_admin',
        company: demoCompany._id,
        isActive: true,
        isEmailVerified: true
      });

      console.log('✅ Demo company and admin created successfully');
    }

  } catch (error) {
    console.error('❌ Database seeding failed:', error.message);
  }
};

// Database health check
const checkDatabaseHealth = async () => {
  try {
    const state = mongoose.connection.readyState;
    const states = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };

    return {
      status: states[state],
      host: mongoose.connection.host,
      name: mongoose.connection.name,
      collections: Object.keys(mongoose.connection.collections).length
    };
  } catch (error) {
    return {
      status: 'error',
      error: error.message
    };
  }
};

// Clear database (for testing)
const clearDatabase = async () => {
  if (process.env.NODE_ENV !== 'test') {
    throw new Error('Database clearing is only allowed in test environment');
  }

  try {
    const collections = mongoose.connection.collections;
    
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }

    console.log('🧹 Database cleared successfully');
  } catch (error) {
    console.error('❌ Database clearing failed:', error.message);
    throw error;
  }
};

module.exports = {
  connectDB,
  seedDatabase,
  checkDatabaseHealth,
  clearDatabase
};
