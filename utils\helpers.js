const crypto = require('crypto');
const nodemailer = require('nodemailer');

// Generate random string
const generateRandomString = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

// Generate booking number
const generateBookingNumber = () => {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  return `BK${timestamp}${random}`;
};

// Calculate booking duration and pricing
const calculateBookingPricing = (pickupDate, dropoffDate, vehiclePricing, addOns = [], additionalDrivers = []) => {
  const pickup = new Date(pickupDate);
  const dropoff = new Date(dropoffDate);
  
  const durationMs = dropoff - pickup;
  const hours = Math.ceil(durationMs / (1000 * 60 * 60));
  const days = Math.ceil(hours / 24);

  let baseRate, rateType, subtotal;

  // Determine rate type and calculate subtotal
  if (hours <= 24) {
    if (hours <= 4 && vehiclePricing.hourly) {
      baseRate = vehiclePricing.hourly;
      rateType = 'hourly';
      subtotal = baseRate * hours;
    } else {
      baseRate = vehiclePricing.daily;
      rateType = 'daily';
      subtotal = baseRate;
    }
  } else if (days <= 7) {
    baseRate = vehiclePricing.daily;
    rateType = 'daily';
    subtotal = baseRate * days;
  } else if (days <= 30 && vehiclePricing.weekly) {
    const weeks = Math.ceil(days / 7);
    baseRate = vehiclePricing.weekly;
    rateType = 'weekly';
    subtotal = baseRate * weeks;
  } else if (vehiclePricing.monthly) {
    const months = Math.ceil(days / 30);
    baseRate = vehiclePricing.monthly;
    rateType = 'monthly';
    subtotal = baseRate * months;
  } else {
    baseRate = vehiclePricing.daily;
    rateType = 'daily';
    subtotal = baseRate * days;
  }

  // Calculate additional costs
  const additionalDriversCost = additionalDrivers.reduce((sum, driver) => sum + (driver.fee || 0), 0);
  const addOnsCost = addOns.reduce((sum, addon) => sum + (addon.cost * addon.quantity), 0);

  // Calculate taxes (configurable rate)
  const taxRate = parseFloat(process.env.TAX_RATE) || 0.10;
  const taxes = (subtotal + additionalDriversCost + addOnsCost) * taxRate;

  const totalAmount = subtotal + additionalDriversCost + addOnsCost + taxes;

  return {
    duration: { hours, days },
    pricing: {
      baseRate,
      rateType,
      subtotal,
      taxes,
      additionalDriversCost,
      addOnsCost,
      totalAmount
    }
  };
};

// Format currency
const formatCurrency = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

// Format date
const formatDate = (date, options = {}) => {
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };

  return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
};

// Email transporter setup
const createEmailTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

// Send email
const sendEmail = async (options) => {
  try {
    const transporter = createEmailTransporter();

    const mailOptions = {
      from: `AutoReserv <${process.env.EMAIL_USER}>`,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('❌ Email sending failed:', error);
    throw error;
  }
};

// Email templates
const emailTemplates = {
  welcome: (user) => ({
    subject: 'Welcome to AutoReserv!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Welcome to AutoReserv!</h1>
        <p>Hello ${user.firstName},</p>
        <p>Thank you for joining AutoReserv. Your account has been created successfully.</p>
        <p>You can now start browsing and booking vehicles from our extensive fleet.</p>
        <div style="margin: 30px 0;">
          <a href="${process.env.CLIENT_URL}/login" 
             style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
            Login to Your Account
          </a>
        </div>
        <p>Best regards,<br>The AutoReserv Team</p>
      </div>
    `
  }),

  bookingConfirmation: (booking) => ({
    subject: `Booking Confirmation - ${booking.bookingNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Booking Confirmed!</h1>
        <p>Hello ${booking.customer.firstName},</p>
        <p>Your booking has been confirmed. Here are the details:</p>
        
        <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Booking Details</h3>
          <p><strong>Booking Number:</strong> ${booking.bookingNumber}</p>
          <p><strong>Vehicle:</strong> ${booking.vehicle.make} ${booking.vehicle.model}</p>
          <p><strong>Pickup:</strong> ${formatDate(booking.pickupDateTime)}</p>
          <p><strong>Dropoff:</strong> ${formatDate(booking.dropoffDateTime)}</p>
          <p><strong>Total Amount:</strong> ${formatCurrency(booking.pricing.totalAmount)}</p>
        </div>
        
        <p>Please arrive at the pickup location 15 minutes before your scheduled time.</p>
        <p>Best regards,<br>The AutoReserv Team</p>
      </div>
    `
  }),

  passwordReset: (user, resetToken) => ({
    subject: 'Password Reset Request',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Password Reset</h1>
        <p>Hello ${user.firstName},</p>
        <p>You requested a password reset for your AutoReserv account.</p>
        <p>Click the button below to reset your password:</p>
        
        <div style="margin: 30px 0;">
          <a href="${process.env.CLIENT_URL}/reset-password?token=${resetToken}" 
             style="background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
            Reset Password
          </a>
        </div>
        
        <p>This link will expire in 10 minutes.</p>
        <p>If you didn't request this, please ignore this email.</p>
        <p>Best regards,<br>The AutoReserv Team</p>
      </div>
    `
  })
};

// Pagination helper
const getPaginationData = (page, limit, total) => {
  const currentPage = parseInt(page) || 1;
  const itemsPerPage = parseInt(limit) || 10;
  const totalPages = Math.ceil(total / itemsPerPage);
  const skip = (currentPage - 1) * itemsPerPage;

  return {
    page: currentPage,
    limit: itemsPerPage,
    total,
    pages: totalPages,
    skip,
    hasNext: currentPage < totalPages,
    hasPrev: currentPage > 1
  };
};

// API response helper
const sendResponse = (res, statusCode, success, message, data = null, meta = null) => {
  const response = {
    success,
    message
  };

  if (data !== null) {
    response.data = data;
  }

  if (meta !== null) {
    response.meta = meta;
  }

  return res.status(statusCode).json(response);
};

// Error response helper
const sendError = (res, statusCode, message, errors = null) => {
  const response = {
    success: false,
    message
  };

  if (errors) {
    response.errors = errors;
  }

  return res.status(statusCode).json(response);
};

// Async handler wrapper
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Generate slug from string
const generateSlug = (text) => {
  return text
    .toLowerCase()
    .replace(/[^a-zA-Z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
};

// Validate file upload
const validateFileUpload = (file, allowedTypes = ['image/jpeg', 'image/png', 'image/webp'], maxSize = 5 * 1024 * 1024) => {
  if (!allowedTypes.includes(file.mimetype)) {
    throw new Error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.');
  }

  if (file.size > maxSize) {
    throw new Error(`File size too large. Maximum size is ${maxSize / (1024 * 1024)}MB.`);
  }

  return true;
};

module.exports = {
  generateRandomString,
  generateBookingNumber,
  calculateBookingPricing,
  formatCurrency,
  formatDate,
  sendEmail,
  emailTemplates,
  getPaginationData,
  sendResponse,
  sendError,
  asyncHandler,
  generateSlug,
  validateFileUpload
};
