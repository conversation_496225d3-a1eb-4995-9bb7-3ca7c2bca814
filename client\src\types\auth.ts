export interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: 'super_admin' | 'company_admin' | 'employee' | 'customer';
  company?: {
    _id: string;
    name: string;
    slug: string;
  };
  avatar?: string;
  isActive: boolean;
  isEmailVerified: boolean;
  lastLogin?: string;
  dateOfBirth?: string;
  drivingLicense?: {
    number: string;
    expiryDate: string;
    country: string;
  };
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    zipCode?: string;
  };
  preferences?: {
    language: 'en' | 'ar' | 'fr';
    currency: 'USD' | 'EUR' | 'MAD' | 'SAR';
    notifications: {
      email: boolean;
      sms: boolean;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
  role: 'customer' | 'company_admin';
  dateOfBirth?: string;
  drivingLicense?: {
    number: string;
    expiryDate: string;
    country: string;
  };
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    zipCode?: string;
  };
  companyData?: {
    name: string;
    description?: string;
    businessLicense: {
      number: string;
      expiryDate: string;
    };
    taxId: string;
    contactInfo: {
      email: string;
      phone: string;
      website?: string;
    };
    headquarters: {
      street: string;
      city: string;
      state?: string;
      country: string;
      zipCode?: string;
    };
  };
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data: {
    user: User;
    token: string;
    company?: any;
  };
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  token: string;
  password: string;
}
