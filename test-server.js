const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
require('dotenv').config();

const app = express();

// Security middleware
app.use(helmet());
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:3000',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Test routes (without database)
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'AutoReserv API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  });
});

app.get('/api/test', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Test endpoint working',
    data: {
      server: 'AutoReserv SaaS',
      features: [
        'Multi-tenant Architecture',
        'Vehicle Fleet Management',
        'Booking System',
        'Payment Processing',
        'Analytics Dashboard',
        'Role-based Access Control'
      ],
      roles: ['super_admin', 'company_admin', 'employee', 'customer'],
      endpoints: {
        auth: '/api/auth/*',
        companies: '/api/companies/*',
        vehicles: '/api/vehicles/*',
        bookings: '/api/bookings/*',
        payments: '/api/payments/*',
        dashboard: '/api/dashboard/*',
        users: '/api/users/*'
      }
    }
  });
});

// Mock data endpoints
app.get('/api/mock/companies', (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      companies: [
        {
          id: '1',
          name: 'Premium Car Rental',
          slug: 'premium-car-rental',
          description: 'Luxury vehicle rental service',
          status: 'active',
          subscription: {
            plan: 'premium',
            status: 'active'
          },
          totalVehicles: 25,
          totalBookings: 150
        },
        {
          id: '2',
          name: 'Budget Auto Rent',
          slug: 'budget-auto-rent',
          description: 'Affordable car rental solutions',
          status: 'active',
          subscription: {
            plan: 'basic',
            status: 'active'
          },
          totalVehicles: 15,
          totalBookings: 89
        }
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 2,
        pages: 1
      }
    }
  });
});

app.get('/api/mock/vehicles', (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      vehicles: [
        {
          id: '1',
          make: 'Toyota',
          model: 'Camry',
          year: 2023,
          category: 'midsize',
          status: 'available',
          pricing: {
            daily: 45,
            deposit: 200
          },
          specifications: {
            seatingCapacity: 5,
            transmission: 'automatic',
            fuelType: 'gasoline'
          }
        },
        {
          id: '2',
          make: 'BMW',
          model: 'X5',
          year: 2023,
          category: 'luxury',
          status: 'available',
          pricing: {
            daily: 120,
            deposit: 500
          },
          specifications: {
            seatingCapacity: 7,
            transmission: 'automatic',
            fuelType: 'gasoline'
          }
        }
      ],
      pagination: {
        page: 1,
        limit: 12,
        total: 2,
        pages: 1
      }
    }
  });
});

app.get('/api/mock/dashboard', (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      vehicles: {
        total: 25,
        available: 18,
        rented: 5,
        maintenance: 2,
        utilization: 20
      },
      bookings: {
        total: 150,
        active: 5,
        completed: 140,
        pending: 5
      },
      revenue: {
        monthly: 15750,
        averageBooking: 105
      },
      recentBookings: [
        {
          id: '1',
          bookingNumber: 'BK202401001',
          customer: { firstName: 'John', lastName: 'Doe' },
          vehicle: { make: 'Toyota', model: 'Camry' },
          status: 'active',
          totalAmount: 135
        }
      ]
    }
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    availableRoutes: [
      'GET /api/health',
      'GET /api/test',
      'GET /api/mock/companies',
      'GET /api/mock/vehicles',
      'GET /api/mock/dashboard'
    ]
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`🚀 AutoReserv Test Server running on port ${PORT}`);
  console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 API URL: http://localhost:${PORT}/api`);
  console.log(`🔍 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`🧪 Test Endpoint: http://localhost:${PORT}/api/test`);
  console.log('');
  console.log('📋 Available Mock Endpoints:');
  console.log(`   • Companies: http://localhost:${PORT}/api/mock/companies`);
  console.log(`   • Vehicles: http://localhost:${PORT}/api/mock/vehicles`);
  console.log(`   • Dashboard: http://localhost:${PORT}/api/mock/dashboard`);
});

module.exports = app;
