// Common types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  meta?: {
    pagination?: PaginationMeta;
  };
  errors?: any[];
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Company types
export interface Company {
  _id: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  businessLicense: {
    number: string;
    expiryDate: string;
    document?: string;
  };
  taxId: string;
  contactInfo: {
    email: string;
    phone: string;
    website?: string;
    socialMedia?: {
      facebook?: string;
      twitter?: string;
      instagram?: string;
      linkedin?: string;
    };
  };
  headquarters: {
    street: string;
    city: string;
    state?: string;
    country: string;
    zipCode?: string;
  };
  branches: Branch[];
  subscription: {
    plan: 'basic' | 'premium' | 'enterprise';
    status: 'active' | 'inactive' | 'suspended' | 'trial';
    startDate: string;
    endDate?: string;
    maxVehicles: number;
    maxUsers: number;
  };
  settings: {
    currency: 'USD' | 'EUR' | 'MAD' | 'SAR';
    timezone: string;
    language: 'en' | 'ar' | 'fr';
    bookingSettings: {
      requireApproval: boolean;
      minBookingDuration: number;
      maxBookingDuration: number;
      advanceBookingLimit: number;
    };
    paymentSettings: {
      acceptCash: boolean;
      acceptCard: boolean;
      requireDeposit: boolean;
      depositAmount: number;
    };
  };
  isActive: boolean;
  isVerified: boolean;
  verificationDate?: string;
  createdBy?: string;
  totalVehicles?: number;
  totalBookings?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Branch {
  _id: string;
  name: string;
  address: {
    street: string;
    city: string;
    state?: string;
    country: string;
    zipCode?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  phone: string;
  email?: string;
  workingHours: {
    [key: string]: {
      open: string;
      close: string;
      closed: boolean;
    };
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Vehicle types
export interface Vehicle {
  _id: string;
  company: string | Company;
  branch: string | Branch;
  make: string;
  model: string;
  year: number;
  color: string;
  licensePlate: string;
  vin: string;
  category: 'economy' | 'compact' | 'midsize' | 'fullsize' | 'luxury' | 'suv' | 'van' | 'truck';
  type: 'sedan' | 'hatchback' | 'coupe' | 'convertible' | 'suv' | 'pickup' | 'van' | 'minivan';
  specifications: {
    engine: string;
    transmission: 'manual' | 'automatic' | 'cvt';
    fuelType: 'gasoline' | 'diesel' | 'hybrid' | 'electric';
    fuelCapacity?: number;
    mileage?: number;
    seatingCapacity: number;
    doors: number;
    airConditioning: boolean;
    gps: boolean;
  };
  features: string[];
  pricing: {
    hourly: number;
    daily: number;
    weekly?: number;
    monthly?: number;
    deposit: number;
  };
  status: 'available' | 'rented' | 'maintenance' | 'out_of_service';
  currentMileage: number;
  lastServiceMileage: number;
  nextServiceMileage?: number;
  insurance: {
    provider: string;
    policyNumber: string;
    expiryDate: string;
    coverage: 'basic' | 'comprehensive' | 'full';
  };
  registration: {
    expiryDate: string;
    document?: string;
  };
  images: {
    url: string;
    caption?: string;
    isPrimary: boolean;
  }[];
  maintenanceRecords: MaintenanceRecord[];
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  currentLocation?: {
    latitude: number;
    longitude: number;
    address: string;
    lastUpdated: string;
  };
  averageRating: number;
  totalReviews: number;
  isActive: boolean;
  createdBy: string;
  displayName?: string;
  primaryImage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MaintenanceRecord {
  _id: string;
  type: 'routine' | 'repair' | 'inspection' | 'cleaning';
  description: string;
  cost: number;
  performedBy?: string;
  performedAt: string;
  nextMaintenanceDate?: string;
  documents: string[];
}

// Booking types
export interface Booking {
  _id: string;
  bookingNumber: string;
  company: string | Company;
  customer: string | User;
  vehicle: string | Vehicle;
  pickupLocation: {
    branch: string | Branch;
    address: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  dropoffLocation: {
    branch: string | Branch;
    address: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  pickupDateTime: string;
  dropoffDateTime: string;
  actualPickupDateTime?: string;
  actualDropoffDateTime?: string;
  duration: {
    hours: number;
    days: number;
  };
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled' | 'no_show';
  pricing: {
    baseRate: number;
    rateType: 'hourly' | 'daily' | 'weekly' | 'monthly';
    subtotal: number;
    taxes: number;
    fees: {
      name: string;
      amount: number;
      description?: string;
    }[];
    discounts: {
      name: string;
      amount: number;
      type: 'percentage' | 'fixed';
    }[];
    deposit: number;
    totalAmount: number;
  };
  payment: {
    method: 'cash' | 'card' | 'bank_transfer' | 'online';
    status: 'pending' | 'paid' | 'partial' | 'refunded' | 'failed';
    paidAmount: number;
    remainingAmount: number;
    transactionId?: string;
    paymentDate?: string;
    refundAmount: number;
    refundDate?: string;
  };
  primaryDriver: {
    name: string;
    license: {
      number: string;
      expiryDate: string;
      country: string;
    };
    phone: string;
    email?: string;
  };
  additionalDrivers: {
    name: string;
    license: {
      number: string;
      expiryDate: string;
      country: string;
    };
    phone?: string;
    email?: string;
    fee: number;
  }[];
  vehicleCondition?: {
    pickup?: VehicleCondition;
    dropoff?: VehicleCondition;
  };
  insurance: {
    type: 'basic' | 'comprehensive' | 'premium';
    cost: number;
    deductible: number;
  };
  addOns: {
    name: string;
    description?: string;
    cost: number;
    quantity: number;
  }[];
  specialRequests?: string;
  notes: {
    author: string | User;
    message: string;
    timestamp: string;
    isInternal: boolean;
  }[];
  review?: {
    rating: number;
    comment: string;
    reviewDate: string;
  };
  createdBy?: string;
  lastModifiedBy?: string;
  cancellation?: {
    reason: string;
    cancelledBy: string | User;
    cancelledAt: string;
    refundAmount: number;
  };
  totalHours?: number;
  totalDays?: number;
  createdAt: string;
  updatedAt: string;
}

export interface VehicleCondition {
  mileage: number;
  fuelLevel: number;
  damages: {
    description: string;
    severity: 'minor' | 'moderate' | 'major';
    images: string[];
    cost?: number;
  }[];
  photos: string[];
  inspectedBy: string | User;
  inspectionDate: string;
  notes?: string;
  additionalCharges?: {
    description: string;
    amount: number;
    reason: string;
  }[];
}

// Dashboard types
export interface DashboardStats {
  vehicles?: {
    total: number;
    available: number;
    rented: number;
    maintenance: number;
    utilization: number;
  };
  bookings?: {
    total: number;
    active: number;
    completed: number;
    pending: number;
  };
  revenue?: {
    monthly: number;
    total?: number;
    averageBooking: number;
  };
  companies?: {
    total: number;
    active: number;
  };
  spending?: {
    total: number;
    average: number;
  };
  recentBookings?: Booking[];
  topVehicles?: any[];
  favoriteVehicles?: any[];
}
