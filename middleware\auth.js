const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Protect routes - verify JWT token
const protect = async (req, res, next) => {
  try {
    let token;

    // Check for token in header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    // Make sure token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user from token
      const user = await User.findById(decoded.id).populate('company');

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'No user found with this token'
        });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(401).json({
          success: false,
          message: 'User account is deactivated'
        });
      }

      req.user = user;
      next();
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Server error in authentication'
    });
  }
};

// Grant access to specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: `User role ${req.user.role} is not authorized to access this route`
      });
    }
    next();
  };
};

// Check if user belongs to the same company (for company_admin and employee)
const checkCompanyAccess = async (req, res, next) => {
  try {
    const user = req.user;

    // Super admin has access to everything
    if (user.role === 'super_admin') {
      return next();
    }

    // For company-specific roles, check company access
    if (user.role === 'company_admin' || user.role === 'employee') {
      // If route has companyId parameter, check if user belongs to that company
      if (req.params.companyId) {
        if (user.company._id.toString() !== req.params.companyId) {
          return res.status(403).json({
            success: false,
            message: 'Not authorized to access this company data'
          });
        }
      }
      
      // Add user's company to request for easy access
      req.company = user.company;
    }

    next();
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Server error in company access check'
    });
  }
};

// Check if user can access specific resource
const checkResourceAccess = (resourceType) => {
  return async (req, res, next) => {
    try {
      const user = req.user;
      const resourceId = req.params.id;

      // Super admin has access to everything
      if (user.role === 'super_admin') {
        return next();
      }

      // For customers, they can only access their own resources
      if (user.role === 'customer') {
        if (resourceType === 'booking') {
          const Booking = require('../models/Booking');
          const booking = await Booking.findById(resourceId);
          
          if (!booking || booking.customer.toString() !== user._id.toString()) {
            return res.status(403).json({
              success: false,
              message: 'Not authorized to access this resource'
            });
          }
        } else if (resourceType === 'user') {
          if (resourceId !== user._id.toString()) {
            return res.status(403).json({
              success: false,
              message: 'Not authorized to access this user data'
            });
          }
        }
      }

      // For company roles, check if resource belongs to their company
      if (user.role === 'company_admin' || user.role === 'employee') {
        if (resourceType === 'vehicle') {
          const Vehicle = require('../models/Vehicle');
          const vehicle = await Vehicle.findById(resourceId);
          
          if (!vehicle || vehicle.company.toString() !== user.company._id.toString()) {
            return res.status(403).json({
              success: false,
              message: 'Not authorized to access this vehicle'
            });
          }
        } else if (resourceType === 'booking') {
          const Booking = require('../models/Booking');
          const booking = await Booking.findById(resourceId);
          
          if (!booking || booking.company.toString() !== user.company._id.toString()) {
            return res.status(403).json({
              success: false,
              message: 'Not authorized to access this booking'
            });
          }
        }
      }

      next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Server error in resource access check'
      });
    }
  };
};

// Rate limiting for sensitive operations
const sensitiveOperationLimit = (req, res, next) => {
  // This would typically use Redis for production
  // For now, we'll use a simple in-memory store
  const attempts = req.session?.sensitiveAttempts || 0;
  
  if (attempts >= 5) {
    return res.status(429).json({
      success: false,
      message: 'Too many sensitive operations. Please try again later.'
    });
  }
  
  next();
};

// Log user activity
const logActivity = (action) => {
  return (req, res, next) => {
    // Log user activity for audit trail
    console.log(`User ${req.user?.email} performed action: ${action} at ${new Date().toISOString()}`);
    
    // In production, this would be stored in a database
    // const ActivityLog = require('../models/ActivityLog');
    // ActivityLog.create({
    //   user: req.user._id,
    //   action,
    //   ip: req.ip,
    //   userAgent: req.get('User-Agent'),
    //   timestamp: new Date()
    // });
    
    next();
  };
};

module.exports = {
  protect,
  authorize,
  checkCompanyAccess,
  checkResourceAccess,
  sensitiveOperationLimit,
  logActivity
};
