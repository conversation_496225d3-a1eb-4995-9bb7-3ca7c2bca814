import React from 'react';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<any>;
  color: 'blue' | 'green' | 'purple' | 'yellow' | 'red';
  change?: string;
  changeType?: 'increase' | 'decrease' | 'neutral';
  onClick?: () => void;
}

export default function StatsCard({
  title,
  value,
  icon: Icon,
  color,
  change,
  changeType = 'neutral',
  onClick
}: StatsCardProps) {
  const colorClasses = {
    blue: 'bg-blue-500 text-blue-600 bg-blue-50',
    green: 'bg-green-500 text-green-600 bg-green-50',
    purple: 'bg-purple-500 text-purple-600 bg-purple-50',
    yellow: 'bg-yellow-500 text-yellow-600 bg-yellow-50',
    red: 'bg-red-500 text-red-600 bg-red-50'
  };

  const [bgColor, textColor, lightBg] = colorClasses[color].split(' ');

  const changeIcon = changeType === 'increase' ? ArrowUpIcon : 
                    changeType === 'decrease' ? ArrowDownIcon : null;

  const changeColorClass = changeType === 'increase' ? 'text-green-600' :
                          changeType === 'decrease' ? 'text-red-600' : 'text-gray-500';

  return (
    <div 
      className={`card hover:shadow-md transition-shadow duration-200 ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={`inline-flex items-center justify-center p-3 ${lightBg} rounded-lg`}>
            <Icon className={`h-6 w-6 ${textColor}`} />
          </div>
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-500 truncate">
              {title}
            </dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-gray-900">
                {typeof value === 'number' ? value.toLocaleString() : value}
              </div>
              {change && (
                <div className={`ml-2 flex items-baseline text-sm font-semibold ${changeColorClass}`}>
                  {changeIcon && <changeIcon className="self-center flex-shrink-0 h-4 w-4" />}
                  <span className="sr-only">
                    {changeType === 'increase' ? 'Increased' : changeType === 'decrease' ? 'Decreased' : 'Changed'} by
                  </span>
                  {change}
                </div>
              )}
            </dd>
          </dl>
        </div>
      </div>
    </div>
  );
}
