import React from 'react';
import { Link } from 'react-router-dom';
import { CalendarIcon, TruckIcon, UserIcon } from '@heroicons/react/24/outline';
import { Booking } from '../../types';

interface RecentBookingsProps {
  bookings: Booking[];
  title?: string;
  showCustomer?: boolean;
  maxItems?: number;
}

export default function RecentBookings({ 
  bookings, 
  title = "Recent Bookings",
  showCustomer = true,
  maxItems = 5 
}: RecentBookingsProps) {
  const displayBookings = bookings.slice(0, maxItems);

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      pending: 'badge badge-warning',
      confirmed: 'badge badge-info',
      active: 'badge badge-success',
      completed: 'badge badge-gray',
      cancelled: 'badge badge-danger',
      no_show: 'badge badge-danger'
    };

    return (
      <span className={statusClasses[status as keyof typeof statusClasses] || 'badge badge-gray'}>
        {status.replace('_', ' ')}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (!displayBookings.length) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        </div>
        <div className="text-center py-12">
          <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No bookings</h3>
          <p className="mt-1 text-sm text-gray-500">
            {showCustomer ? 'No recent bookings found.' : 'You haven\'t made any bookings yet.'}
          </p>
          {!showCustomer && (
            <div className="mt-6">
              <Link to="/vehicles" className="btn-primary">
                Browse Vehicles
              </Link>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <Link 
            to="/dashboard/bookings" 
            className="text-sm text-blue-600 hover:text-blue-500"
          >
            View all
          </Link>
        </div>
      </div>
      
      <div className="flow-root">
        <ul className="-my-5 divide-y divide-gray-200">
          {displayBookings.map((booking) => (
            <li key={booking._id} className="py-5">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center">
                    <TruckIcon className="h-6 w-6 text-gray-600" />
                  </div>
                </div>
                
                <div className="min-w-0 flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {typeof booking.vehicle === 'object' 
                          ? `${booking.vehicle.make} ${booking.vehicle.model}`
                          : 'Vehicle'
                        }
                      </p>
                      <p className="text-sm text-gray-500">
                        Booking #{booking.bookingNumber}
                      </p>
                    </div>
                    <div className="text-right">
                      {getStatusBadge(booking.status)}
                      <p className="text-sm text-gray-500 mt-1">
                        {formatCurrency(booking.pricing.totalAmount)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="mt-2 flex items-center text-sm text-gray-500">
                    <CalendarIcon className="flex-shrink-0 mr-1.5 h-4 w-4" />
                    <span>
                      {formatDate(booking.pickupDateTime)} - {formatDate(booking.dropoffDateTime)}
                    </span>
                  </div>
                  
                  {showCustomer && typeof booking.customer === 'object' && (
                    <div className="mt-1 flex items-center text-sm text-gray-500">
                      <UserIcon className="flex-shrink-0 mr-1.5 h-4 w-4" />
                      <span>
                        {booking.customer.firstName} {booking.customer.lastName}
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="flex-shrink-0">
                  <Link
                    to={`/dashboard/${showCustomer ? 'bookings' : 'my-bookings'}/${booking._id}`}
                    className="text-blue-600 hover:text-blue-500 text-sm font-medium"
                  >
                    View
                  </Link>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
      
      {bookings.length > maxItems && (
        <div className="mt-6 text-center">
          <Link
            to="/dashboard/bookings"
            className="text-sm text-blue-600 hover:text-blue-500 font-medium"
          >
            View {bookings.length - maxItems} more booking{bookings.length - maxItems !== 1 ? 's' : ''}
          </Link>
        </div>
      )}
    </div>
  );
}
