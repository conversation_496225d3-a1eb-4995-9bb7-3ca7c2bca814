import React from 'react';
import { useQuery } from 'react-query';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { dashboardAPI } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';

const COLORS = {
  available: '#10b981', // green
  rented: '#3b82f6',    // blue
  maintenance: '#f59e0b', // yellow
  out_of_service: '#ef4444' // red
};

const STATUS_LABELS = {
  available: 'Available',
  rented: 'Rented',
  maintenance: 'Maintenance',
  out_of_service: 'Out of Service'
};

export default function VehicleUtilization() {
  const { data, isLoading, error } = useQuery(
    'fleet-utilization',
    async () => {
      try {
        const response = await dashboardAPI.getFleet();
        return response.data.data;
      } catch (error) {
        // Mock data for development
        return {
          statusDistribution: [
            { _id: 'available', count: 18 },
            { _id: 'rented', count: 5 },
            { _id: 'maintenance', count: 2 },
            { _id: 'out_of_service', count: 0 }
          ],
          categoryDistribution: [
            { _id: 'economy', count: 8, averageRating: 4.2 },
            { _id: 'midsize', count: 7, averageRating: 4.5 },
            { _id: 'luxury', count: 5, averageRating: 4.8 },
            { _id: 'suv', count: 5, averageRating: 4.3 }
          ]
        };
      }
    },
    {
      refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
    }
  );

  const formatChartData = (statusDistribution: any[]) => {
    return statusDistribution.map(item => ({
      name: STATUS_LABELS[item._id as keyof typeof STATUS_LABELS] || item._id,
      value: item.count,
      status: item._id
    }));
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900">{data.payload.name}</p>
          <p className="text-sm text-gray-600">
            {data.value} vehicle{data.value !== 1 ? 's' : ''}
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // Don't show label for slices less than 5%
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (isLoading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Fleet Utilization</h3>
        </div>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Fleet Utilization</h3>
        </div>
        <div className="text-center py-12">
          <p className="text-gray-500">Failed to load fleet data</p>
        </div>
      </div>
    );
  }

  const chartData = formatChartData(data.statusDistribution || []);
  const totalVehicles = chartData.reduce((sum, item) => sum + item.value, 0);
  const rentedVehicles = chartData.find(item => item.status === 'rented')?.value || 0;
  const utilizationRate = totalVehicles > 0 ? Math.round((rentedVehicles / totalVehicles) * 100) : 0;

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Fleet Utilization</h3>
            <p className="text-sm text-gray-500">Current vehicle status</p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-gray-900">{utilizationRate}%</p>
            <p className="text-sm text-gray-500">Utilization rate</p>
          </div>
        </div>
      </div>

      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={CustomLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {chartData.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={COLORS[entry.status as keyof typeof COLORS] || '#6b7280'} 
                />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>

      {/* Legend */}
      <div className="border-t border-gray-200 pt-4 mt-4">
        <div className="grid grid-cols-2 gap-2">
          {chartData.map((item, index) => (
            <div key={index} className="flex items-center">
              <div 
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: COLORS[item.status as keyof typeof COLORS] || '#6b7280' }}
              />
              <span className="text-sm text-gray-600">
                {item.name}: {item.value}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Category Breakdown */}
      {data.categoryDistribution && data.categoryDistribution.length > 0 && (
        <div className="border-t border-gray-200 pt-4 mt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">By Category</h4>
          <div className="space-y-2">
            {data.categoryDistribution.map((category: any, index: number) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-gray-600 capitalize">
                  {category._id}
                </span>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-900">
                    {category.count}
                  </span>
                  {category.averageRating && (
                    <span className="text-xs text-gray-500">
                      ★ {category.averageRating.toFixed(1)}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
