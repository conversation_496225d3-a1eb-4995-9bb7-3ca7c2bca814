import React from 'react';
import { useQuery } from 'react-query';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts';
import { dashboardAPI } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';

interface RevenueChartProps {
  period?: '7d' | '30d' | '90d' | '1y';
  type?: 'line' | 'bar';
}

export default function RevenueChart({ 
  period = '30d', 
  type = 'line' 
}: RevenueChartProps) {
  const { data, isLoading, error } = useQuery(
    ['revenue-chart', period],
    async () => {
      try {
        const response = await dashboardAPI.getRevenue({ period });
        return response.data.data;
      } catch (error) {
        // Mock data for development
        return {
          dailyRevenue: [
            { _id: { year: 2024, month: 1, day: 1 }, revenue: 1200, bookings: 8 },
            { _id: { year: 2024, month: 1, day: 2 }, revenue: 1500, bookings: 10 },
            { _id: { year: 2024, month: 1, day: 3 }, revenue: 900, bookings: 6 },
            { _id: { year: 2024, month: 1, day: 4 }, revenue: 1800, bookings: 12 },
            { _id: { year: 2024, month: 1, day: 5 }, revenue: 2100, bookings: 14 },
            { _id: { year: 2024, month: 1, day: 6 }, revenue: 1600, bookings: 11 },
            { _id: { year: 2024, month: 1, day: 7 }, revenue: 1400, bookings: 9 },
          ],
          totals: {
            totalRevenue: 10500,
            totalBookings: 70,
            averageBookingValue: 150
          }
        };
      }
    },
    {
      refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    }
  );

  const formatChartData = (dailyRevenue: any[]) => {
    return dailyRevenue.map(item => ({
      date: `${item._id.month}/${item._id.day}`,
      revenue: item.revenue,
      bookings: item.bookings,
      fullDate: new Date(item._id.year, item._id.month - 1, item._id.day)
    })).sort((a, b) => a.fullDate.getTime() - b.fullDate.getTime());
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900">{`Date: ${label}`}</p>
          <p className="text-sm text-blue-600">
            {`Revenue: ${formatCurrency(payload[0].value)}`}
          </p>
          <p className="text-sm text-green-600">
            {`Bookings: ${payload[1]?.value || payload[0].payload.bookings}`}
          </p>
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Revenue Overview</h3>
        </div>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Revenue Overview</h3>
        </div>
        <div className="text-center py-12">
          <p className="text-gray-500">Failed to load revenue data</p>
        </div>
      </div>
    );
  }

  const chartData = formatChartData(data.dailyRevenue || []);
  const totals = data.totals || {};

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Revenue Overview</h3>
            <p className="text-sm text-gray-500">
              Last {period === '7d' ? '7 days' : period === '30d' ? '30 days' : period === '90d' ? '90 days' : 'year'}
            </p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(totals.totalRevenue || 0)}
            </p>
            <p className="text-sm text-gray-500">
              {totals.totalBookings || 0} bookings
            </p>
          </div>
        </div>
      </div>

      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          {type === 'line' ? (
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="date" 
                stroke="#6b7280"
                fontSize={12}
              />
              <YAxis 
                stroke="#6b7280"
                fontSize={12}
                tickFormatter={formatCurrency}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey="revenue"
                stroke="#2563eb"
                strokeWidth={2}
                dot={{ fill: '#2563eb', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#2563eb', strokeWidth: 2 }}
              />
            </LineChart>
          ) : (
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="date" 
                stroke="#6b7280"
                fontSize={12}
              />
              <YAxis 
                stroke="#6b7280"
                fontSize={12}
                tickFormatter={formatCurrency}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar
                dataKey="revenue"
                fill="#2563eb"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>

      {/* Summary Stats */}
      <div className="border-t border-gray-200 pt-4 mt-4">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-sm text-gray-500">Total Revenue</p>
            <p className="text-lg font-semibold text-gray-900">
              {formatCurrency(totals.totalRevenue || 0)}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Total Bookings</p>
            <p className="text-lg font-semibold text-gray-900">
              {totals.totalBookings || 0}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Avg Booking</p>
            <p className="text-lg font-semibold text-gray-900">
              {formatCurrency(totals.averageBookingValue || 0)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
