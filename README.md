# AutoReserv - Car Rental SaaS Platform

AutoReserv is a comprehensive Software-as-a-Service (SaaS) platform designed for car rental companies. It provides a complete solution for managing vehicle fleets, bookings, customers, and business operations with a modern, professional dashboard inspired by CJ Affiliate's design.

## 🚀 Features

### Core Functionality
- **Multi-tenant Architecture**: Support for multiple car rental companies
- **User Management**: Role-based access control (Super Admin, Company Admin, Employee, Customer)
- **Fleet Management**: Complete vehicle inventory management with maintenance tracking
- **Booking System**: Advanced reservation system with real-time availability
- **Payment Processing**: Integrated payment system with Stripe support
- **Dashboard Analytics**: Comprehensive reporting and analytics

### User Roles
- **Super Admin**: System-wide management and oversight
- **Company Admin**: Full company management capabilities
- **Employee**: Day-to-day operations management
- **Customer**: Vehicle browsing and booking

### Key Features
- **Vehicle Management**: Add, edit, and track vehicles with detailed specifications
- **Booking Management**: Handle reservations, contracts, and vehicle handovers
- **Customer Management**: Maintain customer profiles and booking history
- **Payment System**: Process payments, refunds, and manage billing
- **Analytics Dashboard**: Revenue tracking, fleet utilization, and performance metrics
- **Multi-branch Support**: Manage multiple locations and branches
- **Maintenance Tracking**: Vehicle service history and scheduling

## 🛠 Technology Stack

### Backend
- **Node.js** with Express.js framework
- **MongoDB** with Mongoose ODM
- **JWT** for authentication
- **Stripe** for payment processing
- **Cloudinary** for image storage
- **Nodemailer** for email notifications

### Security & Performance
- **Helmet** for security headers
- **Rate limiting** for API protection
- **Data validation** with express-validator
- **Password hashing** with bcryptjs
- **CORS** configuration

## 📋 Prerequisites

- Node.js (v16 or higher)
- MongoDB (v4.4 or higher)
- Stripe account for payment processing
- Cloudinary account for image storage

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd autoreserv-saas
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your configuration:
   ```env
   # Database
   MONGODB_URI=mongodb://localhost:27017/autoreserv
   
   # JWT
   JWT_SECRET=your_super_secret_jwt_key_here
   JWT_EXPIRE=7d
   
   # Server
   PORT=5000
   NODE_ENV=development
   
   # Cloudinary
   CLOUDINARY_CLOUD_NAME=your_cloud_name
   CLOUDINARY_API_KEY=your_api_key
   CLOUDINARY_API_SECRET=your_api_secret
   
   # Stripe
   STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
   STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
   
   # Email
   EMAIL_HOST=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your_app_password
   ```

4. **Start the server**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update user profile
- `PUT /api/auth/change-password` - Change password

### Company Management
- `GET /api/companies` - Get all companies
- `GET /api/companies/:id` - Get single company
- `PUT /api/companies/:id` - Update company
- `POST /api/companies/:id/branches` - Add branch
- `PUT /api/companies/:id/branches/:branchId` - Update branch

### Vehicle Management
- `GET /api/vehicles` - Get all vehicles (with filters)
- `GET /api/vehicles/:id` - Get single vehicle
- `POST /api/vehicles` - Create new vehicle
- `PUT /api/vehicles/:id` - Update vehicle
- `DELETE /api/vehicles/:id` - Delete vehicle

### Booking Management
- `GET /api/bookings` - Get all bookings
- `GET /api/bookings/:id` - Get single booking
- `POST /api/bookings` - Create new booking
- `PUT /api/bookings/:id/status` - Update booking status
- `POST /api/bookings/:id/notes` - Add note to booking

### Payment Processing
- `POST /api/payments/create-intent` - Create payment intent
- `POST /api/payments/confirm` - Confirm payment
- `POST /api/payments/cash` - Process cash payment
- `POST /api/payments/refund` - Process refund

### Dashboard Analytics
- `GET /api/dashboard/overview` - Dashboard overview
- `GET /api/dashboard/revenue` - Revenue analytics
- `GET /api/dashboard/bookings` - Booking analytics
- `GET /api/dashboard/fleet` - Fleet utilization

## 🏗 Database Schema

### User Model
- Personal information and authentication
- Role-based permissions
- Company associations
- Customer-specific fields (driving license, etc.)

### Company Model
- Company details and settings
- Multi-branch support
- Subscription management
- Business license information

### Vehicle Model
- Complete vehicle specifications
- Pricing and availability
- Maintenance records
- Image gallery

### Booking Model
- Reservation details
- Pricing calculations
- Payment tracking
- Vehicle condition reports

## 🔐 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Role-based Access Control**: Granular permissions system
- **Data Validation**: Input validation and sanitization
- **Rate Limiting**: API abuse prevention
- **Password Hashing**: Secure password storage
- **CORS Protection**: Cross-origin request security

## 📊 Dashboard Features

The dashboard is designed with inspiration from CJ Affiliate, featuring:
- **Clean, Professional Design**: Modern UI with intuitive navigation
- **Real-time Analytics**: Live data updates and metrics
- **Interactive Charts**: Revenue and performance visualizations
- **Responsive Layout**: Works on all device sizes
- **Role-based Views**: Customized dashboards per user role

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

### Production Setup
1. Set `NODE_ENV=production` in environment variables
2. Configure production database
3. Set up SSL certificates
4. Configure reverse proxy (nginx recommended)
5. Set up process manager (PM2 recommended)

### Environment Variables for Production
```env
NODE_ENV=production
MONGODB_URI=mongodb://your-production-db
JWT_SECRET=your-production-jwt-secret
# ... other production configurations
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📞 Support

For support and questions, please contact:
- Email: <EMAIL>
- Documentation: [Link to documentation]
- Issues: [GitHub Issues]

## 🔄 Version History

- **v1.0.0** - Initial release with core functionality
- Backend API complete with authentication, vehicle management, booking system, and payment processing
- Dashboard analytics and reporting features
- Multi-tenant architecture support

---

**AutoReserv** - Empowering car rental businesses with modern technology solutions.
