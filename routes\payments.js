const express = require('express');
const { body, validationResult } = require('express-validator');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const Booking = require('../models/Booking');
const { protect, checkResourceAccess } = require('../middleware/auth');

const router = express.Router();

// @desc    Create payment intent for booking
// @route   POST /api/payments/create-intent
// @access  Private
router.post('/create-intent', protect, [
  body('bookingId').isMongoId().withMessage('Valid booking ID is required'),
  body('amount').isFloat({ min: 0.01 }).withMessage('Valid amount is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { bookingId, amount } = req.body;

    // Get booking details
    const booking = await Booking.findById(bookingId)
      .populate('customer', 'firstName lastName email')
      .populate('vehicle', 'make model year')
      .populate('company', 'name');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if user can pay for this booking
    if (booking.customer._id.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to pay for this booking'
      });
    }

    // Check if booking is in correct status
    if (!['pending', 'confirmed'].includes(booking.status)) {
      return res.status(400).json({
        success: false,
        message: 'Cannot process payment for this booking status'
      });
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: 'usd',
      customer: booking.customer.stripeCustomerId, // You'd need to create this when user registers
      metadata: {
        bookingId: booking._id.toString(),
        customerId: booking.customer._id.toString(),
        companyId: booking.company._id.toString()
      },
      description: `Payment for ${booking.vehicle.make} ${booking.vehicle.model} rental - Booking #${booking.bookingNumber}`
    });

    res.status(200).json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id
      }
    });

  } catch (error) {
    console.error('Create payment intent error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during payment intent creation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Confirm payment
// @route   POST /api/payments/confirm
// @access  Private
router.post('/confirm', protect, [
  body('paymentIntentId').notEmpty().withMessage('Payment intent ID is required'),
  body('bookingId').isMongoId().withMessage('Valid booking ID is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { paymentIntentId, bookingId } = req.body;

    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== 'succeeded') {
      return res.status(400).json({
        success: false,
        message: 'Payment not successful'
      });
    }

    // Get booking
    const booking = await Booking.findById(bookingId);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Update booking payment status
    const paidAmount = paymentIntent.amount / 100; // Convert from cents
    
    booking.payment.status = 'paid';
    booking.payment.paidAmount += paidAmount;
    booking.payment.remainingAmount = Math.max(0, booking.pricing.totalAmount - booking.payment.paidAmount);
    booking.payment.transactionId = paymentIntent.id;
    booking.payment.paymentDate = new Date();
    booking.payment.method = 'card';

    // If fully paid and was pending, confirm the booking
    if (booking.payment.remainingAmount === 0 && booking.status === 'pending') {
      booking.status = 'confirmed';
    }

    await booking.save();

    res.status(200).json({
      success: true,
      message: 'Payment confirmed successfully',
      data: {
        booking: {
          id: booking._id,
          bookingNumber: booking.bookingNumber,
          status: booking.status,
          payment: booking.payment
        }
      }
    });

  } catch (error) {
    console.error('Confirm payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during payment confirmation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Process cash payment
// @route   POST /api/payments/cash
// @access  Private (Company Admin, Employee)
router.post('/cash', protect, authorize('company_admin', 'employee'), [
  body('bookingId').isMongoId().withMessage('Valid booking ID is required'),
  body('amount').isFloat({ min: 0.01 }).withMessage('Valid amount is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { bookingId, amount, notes } = req.body;

    // Get booking
    const booking = await Booking.findById(bookingId);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if booking belongs to user's company
    if (req.user.role !== 'super_admin' && 
        booking.company.toString() !== req.user.company._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to process payment for this booking'
      });
    }

    // Update payment
    booking.payment.paidAmount += amount;
    booking.payment.remainingAmount = Math.max(0, booking.pricing.totalAmount - booking.payment.paidAmount);
    booking.payment.method = 'cash';
    booking.payment.paymentDate = new Date();
    
    if (booking.payment.remainingAmount === 0) {
      booking.payment.status = 'paid';
      if (booking.status === 'pending') {
        booking.status = 'confirmed';
      }
    } else {
      booking.payment.status = 'partial';
    }

    // Add note about cash payment
    booking.notes.push({
      author: req.user._id,
      message: `Cash payment received: $${amount}${notes ? ` - ${notes}` : ''}`,
      isInternal: true,
      timestamp: new Date()
    });

    await booking.save();

    const updatedBooking = await Booking.findById(booking._id)
      .populate('customer', 'firstName lastName email')
      .populate('vehicle', 'make model year');

    res.status(200).json({
      success: true,
      message: 'Cash payment processed successfully',
      data: updatedBooking
    });

  } catch (error) {
    console.error('Process cash payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during cash payment processing',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Process refund
// @route   POST /api/payments/refund
// @access  Private (Company Admin, Super Admin)
router.post('/refund', protect, authorize('company_admin', 'super_admin'), [
  body('bookingId').isMongoId().withMessage('Valid booking ID is required'),
  body('amount').isFloat({ min: 0.01 }).withMessage('Valid refund amount is required'),
  body('reason').notEmpty().withMessage('Refund reason is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { bookingId, amount, reason } = req.body;

    // Get booking
    const booking = await Booking.findById(bookingId);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if booking belongs to user's company
    if (req.user.role !== 'super_admin' && 
        booking.company.toString() !== req.user.company._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to process refund for this booking'
      });
    }

    // Check if refund amount is valid
    if (amount > booking.payment.paidAmount) {
      return res.status(400).json({
        success: false,
        message: 'Refund amount cannot exceed paid amount'
      });
    }

    let refundResult = null;

    // Process refund through Stripe if original payment was by card
    if (booking.payment.method === 'card' && booking.payment.transactionId) {
      try {
        refundResult = await stripe.refunds.create({
          payment_intent: booking.payment.transactionId,
          amount: Math.round(amount * 100), // Convert to cents
          reason: 'requested_by_customer',
          metadata: {
            bookingId: booking._id.toString(),
            refundReason: reason
          }
        });
      } catch (stripeError) {
        console.error('Stripe refund error:', stripeError);
        return res.status(400).json({
          success: false,
          message: 'Failed to process refund through payment provider'
        });
      }
    }

    // Update booking
    booking.payment.refundAmount += amount;
    booking.payment.refundDate = new Date();
    booking.payment.paidAmount -= amount;
    booking.payment.remainingAmount = Math.max(0, booking.pricing.totalAmount - booking.payment.paidAmount);
    
    if (booking.payment.paidAmount === 0) {
      booking.payment.status = 'refunded';
    } else {
      booking.payment.status = 'partial';
    }

    // Add note about refund
    booking.notes.push({
      author: req.user._id,
      message: `Refund processed: $${amount} - Reason: ${reason}`,
      isInternal: true,
      timestamp: new Date()
    });

    await booking.save();

    res.status(200).json({
      success: true,
      message: 'Refund processed successfully',
      data: {
        refundAmount: amount,
        refundId: refundResult?.id,
        booking: {
          id: booking._id,
          payment: booking.payment
        }
      }
    });

  } catch (error) {
    console.error('Process refund error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during refund processing',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get payment history for booking
// @route   GET /api/payments/booking/:id/history
// @access  Private
router.get('/booking/:id/history', protect, checkResourceAccess('booking'), async (req, res) => {
  try {
    const booking = await Booking.findById(req.params.id)
      .populate('customer', 'firstName lastName email')
      .select('bookingNumber payment pricing notes');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Get payment-related notes
    const paymentNotes = booking.notes.filter(note => 
      note.message.toLowerCase().includes('payment') || 
      note.message.toLowerCase().includes('refund')
    );

    const paymentHistory = {
      booking: {
        id: booking._id,
        bookingNumber: booking.bookingNumber,
        customer: booking.customer
      },
      pricing: booking.pricing,
      payment: booking.payment,
      paymentNotes
    };

    res.status(200).json({
      success: true,
      data: paymentHistory
    });

  } catch (error) {
    console.error('Get payment history error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Stripe webhook handler
// @route   POST /api/payments/webhook
// @access  Public (Stripe webhook)
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      console.log('Payment succeeded:', paymentIntent.id);
      
      // Update booking status if needed
      if (paymentIntent.metadata.bookingId) {
        try {
          const booking = await Booking.findById(paymentIntent.metadata.bookingId);
          if (booking && booking.payment.status === 'pending') {
            booking.payment.status = 'paid';
            booking.payment.transactionId = paymentIntent.id;
            booking.payment.paymentDate = new Date();
            await booking.save();
          }
        } catch (error) {
          console.error('Error updating booking after payment:', error);
        }
      }
      break;

    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      console.log('Payment failed:', failedPayment.id);
      break;

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({ received: true });
});

module.exports = router;
