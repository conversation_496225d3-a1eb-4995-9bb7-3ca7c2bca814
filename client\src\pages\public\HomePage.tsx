import React from 'react';
import { Link } from 'react-router-dom';
import { 
  TruckIcon, 
  CalendarIcon, 
  CurrencyDollarIcon, 
  ShieldCheckIcon,
  ChartBarIcon,
  UsersIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    name: 'Fleet Management',
    description: 'Comprehensive vehicle inventory management with maintenance tracking and real-time status updates.',
    icon: TruckIcon,
  },
  {
    name: 'Smart Booking System',
    description: 'Advanced reservation system with real-time availability, automated pricing, and contract management.',
    icon: CalendarIcon,
  },
  {
    name: 'Payment Processing',
    description: 'Integrated payment system supporting multiple payment methods with automated invoicing.',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'Analytics & Reports',
    description: 'Comprehensive dashboard with revenue tracking, fleet utilization, and performance metrics.',
    icon: ChartBarIcon,
  },
  {
    name: 'Multi-tenant Architecture',
    description: 'Support for multiple car rental companies with role-based access control and company isolation.',
    icon: UsersIcon,
  },
  {
    name: 'Enterprise Security',
    description: 'Bank-level security with data encryption, secure authentication, and compliance features.',
    icon: ShieldCheckIcon,
  },
];

const stats = [
  { id: 1, name: 'Companies using AutoReserv', value: '50+' },
  { id: 2, name: 'Vehicles managed', value: '2,000+' },
  { id: 3, name: 'Bookings processed', value: '10,000+' },
  { id: 4, name: 'Revenue generated', value: '$2M+' },
];

export default function HomePage() {
  return (
    <div className="bg-white">
      {/* Hero section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80">
          <div className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]" />
        </div>
        
        <div className="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Modern Car Rental Management System
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Streamline your car rental business with our comprehensive SaaS platform. 
              Manage fleets, bookings, customers, and payments all in one place.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                to="/register"
                className="rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
              >
                Get started
              </Link>
              <Link
                to="/vehicles"
                className="text-sm font-semibold leading-6 text-gray-900"
              >
                Browse vehicles <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
        </div>
        
        <div className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]">
          <div className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]" />
        </div>
      </div>

      {/* Feature section */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-24 sm:py-32">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base font-semibold leading-7 text-blue-600">Everything you need</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Complete car rental management solution
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            AutoReserv provides all the tools you need to run a successful car rental business, 
            from fleet management to customer service.
          </p>
        </div>
        
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16">
            {features.map((feature) => (
              <div key={feature.name} className="relative pl-16">
                <dt className="text-base font-semibold leading-7 text-gray-900">
                  <div className="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                    <feature.icon className="h-6 w-6 text-white" aria-hidden="true" />
                  </div>
                  {feature.name}
                </dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">{feature.description}</dd>
              </div>
            ))}
          </dl>
        </div>
      </div>

      {/* Stats section */}
      <div className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:max-w-none">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Trusted by car rental businesses worldwide
              </h2>
              <p className="mt-4 text-lg leading-8 text-gray-600">
                Join thousands of businesses that trust AutoReserv to manage their operations.
              </p>
            </div>
            <dl className="mt-16 grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center sm:grid-cols-2 lg:grid-cols-4">
              {stats.map((stat) => (
                <div key={stat.id} className="flex flex-col bg-white p-8">
                  <dt className="text-sm font-semibold leading-6 text-gray-600">{stat.name}</dt>
                  <dd className="order-first text-3xl font-bold tracking-tight text-blue-600">
                    {stat.value}
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="bg-blue-600">
        <div className="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Ready to transform your car rental business?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-blue-100">
              Start your free trial today and see how AutoReserv can streamline your operations 
              and boost your revenue.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                to="/register"
                className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-blue-600 shadow-sm hover:bg-blue-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
              >
                Start free trial
              </Link>
              <Link
                to="/contact"
                className="text-sm font-semibold leading-6 text-white"
              >
                Contact sales <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
