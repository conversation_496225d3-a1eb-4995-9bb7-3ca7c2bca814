const { body, param, query } = require('express-validator');

// Common validation rules
const commonValidations = {
  // MongoDB ObjectId validation
  mongoId: (field = 'id') => 
    param(field).isMongoId().withMessage(`Invalid ${field} format`),

  // Email validation
  email: (field = 'email') =>
    body(field)
      .isEmail()
      .normalizeEmail()
      .withMessage('Please enter a valid email address'),

  // Password validation
  password: (field = 'password', minLength = 6) =>
    body(field)
      .isLength({ min: minLength })
      .withMessage(`Password must be at least ${minLength} characters long`)
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),

  // Phone validation
  phone: (field = 'phone') =>
    body(field)
      .isMobilePhone()
      .withMessage('Please enter a valid phone number'),

  // Name validation
  name: (field, minLength = 2, maxLength = 50) =>
    body(field)
      .trim()
      .isLength({ min: minLength, max: maxLength })
      .withMessage(`${field} must be between ${minLength} and ${maxLength} characters`)
      .matches(/^[a-zA-Z\s]+$/)
      .withMessage(`${field} can only contain letters and spaces`),

  // Date validation
  date: (field) =>
    body(field)
      .isISO8601()
      .withMessage(`${field} must be a valid date`),

  // Future date validation
  futureDate: (field) =>
    body(field)
      .isISO8601()
      .withMessage(`${field} must be a valid date`)
      .custom((value) => {
        if (new Date(value) <= new Date()) {
          throw new Error(`${field} must be in the future`);
        }
        return true;
      }),

  // Positive number validation
  positiveNumber: (field) =>
    body(field)
      .isFloat({ min: 0.01 })
      .withMessage(`${field} must be a positive number`),

  // Pagination validation
  pagination: () => [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ]
};

// User validation rules
const userValidations = {
  register: [
    commonValidations.name('firstName'),
    commonValidations.name('lastName'),
    commonValidations.email(),
    commonValidations.password(),
    commonValidations.phone(),
    body('role')
      .isIn(['customer', 'company_admin'])
      .withMessage('Invalid role'),
    body('dateOfBirth')
      .optional()
      .isISO8601()
      .withMessage('Date of birth must be a valid date')
      .custom((value) => {
        const age = (new Date() - new Date(value)) / (365.25 * 24 * 60 * 60 * 1000);
        if (age < 18) {
          throw new Error('Must be at least 18 years old');
        }
        return true;
      }),
    body('drivingLicense.number')
      .if(body('role').equals('customer'))
      .notEmpty()
      .withMessage('Driving license number is required for customers'),
    body('drivingLicense.expiryDate')
      .if(body('role').equals('customer'))
      .isISO8601()
      .withMessage('Driving license expiry date must be valid')
      .custom((value) => {
        if (new Date(value) <= new Date()) {
          throw new Error('Driving license must not be expired');
        }
        return true;
      })
  ],

  login: [
    commonValidations.email(),
    body('password').notEmpty().withMessage('Password is required')
  ],

  updateProfile: [
    commonValidations.name('firstName').optional(),
    commonValidations.name('lastName').optional(),
    commonValidations.phone().optional()
  ],

  changePassword: [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    commonValidations.password('newPassword')
  ]
};

// Company validation rules
const companyValidations = {
  create: [
    body('name')
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Company name must be between 2 and 100 characters'),
    body('description')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Description cannot exceed 500 characters'),
    body('businessLicense.number')
      .notEmpty()
      .withMessage('Business license number is required'),
    body('businessLicense.expiryDate')
      .isISO8601()
      .withMessage('Business license expiry date must be valid')
      .custom((value) => {
        if (new Date(value) <= new Date()) {
          throw new Error('Business license must not be expired');
        }
        return true;
      }),
    body('taxId')
      .notEmpty()
      .withMessage('Tax ID is required'),
    commonValidations.email('contactInfo.email'),
    commonValidations.phone('contactInfo.phone')
  ],

  addBranch: [
    body('name')
      .trim()
      .isLength({ min: 2 })
      .withMessage('Branch name is required'),
    body('address.street')
      .notEmpty()
      .withMessage('Street address is required'),
    body('address.city')
      .notEmpty()
      .withMessage('City is required'),
    body('address.country')
      .notEmpty()
      .withMessage('Country is required'),
    commonValidations.phone()
  ]
};

// Vehicle validation rules
const vehicleValidations = {
  create: [
    body('make')
      .trim()
      .isLength({ min: 2 })
      .withMessage('Vehicle make is required'),
    body('model')
      .trim()
      .isLength({ min: 2 })
      .withMessage('Vehicle model is required'),
    body('year')
      .isInt({ min: 1990, max: new Date().getFullYear() + 1 })
      .withMessage('Invalid year'),
    body('color')
      .notEmpty()
      .withMessage('Vehicle color is required'),
    body('licensePlate')
      .notEmpty()
      .withMessage('License plate is required')
      .matches(/^[A-Z0-9-]+$/)
      .withMessage('License plate format is invalid'),
    body('vin')
      .isLength({ min: 17, max: 17 })
      .withMessage('VIN must be 17 characters')
      .matches(/^[A-HJ-NPR-Z0-9]+$/)
      .withMessage('VIN format is invalid'),
    body('category')
      .isIn(['economy', 'compact', 'midsize', 'fullsize', 'luxury', 'suv', 'van', 'truck'])
      .withMessage('Invalid category'),
    body('type')
      .isIn(['sedan', 'hatchback', 'coupe', 'convertible', 'suv', 'pickup', 'van', 'minivan'])
      .withMessage('Invalid type'),
    commonValidations.positiveNumber('pricing.daily'),
    commonValidations.positiveNumber('pricing.deposit'),
    body('specifications.seatingCapacity')
      .isInt({ min: 1, max: 15 })
      .withMessage('Seating capacity must be between 1 and 15'),
    body('specifications.transmission')
      .isIn(['manual', 'automatic', 'cvt'])
      .withMessage('Invalid transmission type'),
    body('specifications.fuelType')
      .isIn(['gasoline', 'diesel', 'hybrid', 'electric'])
      .withMessage('Invalid fuel type')
  ]
};

// Booking validation rules
const bookingValidations = {
  create: [
    commonValidations.mongoId('vehicle'),
    commonValidations.futureDate('pickupDateTime'),
    commonValidations.futureDate('dropoffDateTime'),
    body('dropoffDateTime')
      .custom((value, { req }) => {
        if (new Date(value) <= new Date(req.body.pickupDateTime)) {
          throw new Error('Dropoff date must be after pickup date');
        }
        return true;
      }),
    commonValidations.mongoId('pickupLocation.branch'),
    commonValidations.mongoId('dropoffLocation.branch'),
    body('primaryDriver.name')
      .notEmpty()
      .withMessage('Primary driver name is required'),
    body('primaryDriver.license.number')
      .notEmpty()
      .withMessage('Driver license number is required'),
    commonValidations.phone('primaryDriver.phone')
  ],

  updateStatus: [
    body('status')
      .isIn(['pending', 'confirmed', 'active', 'completed', 'cancelled', 'no_show'])
      .withMessage('Invalid status')
  ]
};

// Payment validation rules
const paymentValidations = {
  createIntent: [
    commonValidations.mongoId('bookingId'),
    commonValidations.positiveNumber('amount')
  ],

  confirmPayment: [
    body('paymentIntentId')
      .notEmpty()
      .withMessage('Payment intent ID is required'),
    commonValidations.mongoId('bookingId')
  ],

  cashPayment: [
    commonValidations.mongoId('bookingId'),
    commonValidations.positiveNumber('amount')
  ],

  refund: [
    commonValidations.mongoId('bookingId'),
    commonValidations.positiveNumber('amount'),
    body('reason')
      .notEmpty()
      .withMessage('Refund reason is required')
  ]
};

module.exports = {
  commonValidations,
  userValidations,
  companyValidations,
  vehicleValidations,
  bookingValidations,
  paymentValidations
};
