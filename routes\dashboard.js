const express = require('express');
const mongoose = require('mongoose');
const Company = require('../models/Company');
const Vehicle = require('../models/Vehicle');
const Booking = require('../models/Booking');
const User = require('../models/User');
const { protect, authorize, checkCompanyAccess } = require('../middleware/auth');

const router = express.Router();

// @desc    Get dashboard overview
// @route   GET /api/dashboard/overview
// @access  Private
router.get('/overview', protect, async (req, res) => {
  try {
    let stats = {};

    if (req.user.role === 'super_admin') {
      // Super admin dashboard - system-wide stats
      const [
        totalCompanies,
        activeCompanies,
        totalVehicles,
        totalBookings,
        totalRevenue,
        recentBookings
      ] = await Promise.all([
        Company.countDocuments(),
        Company.countDocuments({ isActive: true, 'subscription.status': 'active' }),
        Vehicle.countDocuments({ isActive: true }),
        Booking.countDocuments(),
        Booking.aggregate([
          { $match: { status: 'completed' } },
          { $group: { _id: null, total: { $sum: '$pricing.totalAmount' } } }
        ]),
        Booking.find()
          .populate('customer', 'firstName lastName')
          .populate('vehicle', 'make model')
          .populate('company', 'name')
          .sort({ createdAt: -1 })
          .limit(5)
      ]);

      stats = {
        companies: {
          total: totalCompanies,
          active: activeCompanies
        },
        vehicles: {
          total: totalVehicles
        },
        bookings: {
          total: totalBookings
        },
        revenue: {
          total: totalRevenue[0]?.total || 0
        },
        recentBookings
      };

    } else if (req.user.role === 'company_admin' || req.user.role === 'employee') {
      // Company dashboard
      const companyId = req.user.company._id;
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const [
        totalVehicles,
        availableVehicles,
        rentedVehicles,
        maintenanceVehicles,
        totalBookings,
        activeBookings,
        completedBookings,
        pendingBookings,
        monthlyRevenue,
        recentBookings,
        topVehicles
      ] = await Promise.all([
        Vehicle.countDocuments({ company: companyId, isActive: true }),
        Vehicle.countDocuments({ company: companyId, status: 'available', isActive: true }),
        Vehicle.countDocuments({ company: companyId, status: 'rented', isActive: true }),
        Vehicle.countDocuments({ company: companyId, status: 'maintenance', isActive: true }),
        Booking.countDocuments({ company: companyId }),
        Booking.countDocuments({ company: companyId, status: 'active' }),
        Booking.countDocuments({ company: companyId, status: 'completed' }),
        Booking.countDocuments({ company: companyId, status: 'pending' }),
        Booking.aggregate([
          {
            $match: {
              company: new mongoose.Types.ObjectId(companyId),
              status: 'completed',
              createdAt: { $gte: thirtyDaysAgo }
            }
          },
          { $group: { _id: null, total: { $sum: '$pricing.totalAmount' } } }
        ]),
        Booking.find({ company: companyId })
          .populate('customer', 'firstName lastName')
          .populate('vehicle', 'make model licensePlate')
          .sort({ createdAt: -1 })
          .limit(10),
        Booking.aggregate([
          {
            $match: {
              company: new mongoose.Types.ObjectId(companyId),
              status: 'completed'
            }
          },
          {
            $group: {
              _id: '$vehicle',
              bookings: { $sum: 1 },
              revenue: { $sum: '$pricing.totalAmount' }
            }
          },
          { $sort: { bookings: -1 } },
          { $limit: 5 },
          {
            $lookup: {
              from: 'vehicles',
              localField: '_id',
              foreignField: '_id',
              as: 'vehicle'
            }
          },
          { $unwind: '$vehicle' }
        ])
      ]);

      stats = {
        vehicles: {
          total: totalVehicles,
          available: availableVehicles,
          rented: rentedVehicles,
          maintenance: maintenanceVehicles,
          utilization: totalVehicles > 0 ? Math.round((rentedVehicles / totalVehicles) * 100) : 0
        },
        bookings: {
          total: totalBookings,
          active: activeBookings,
          completed: completedBookings,
          pending: pendingBookings
        },
        revenue: {
          monthly: monthlyRevenue[0]?.total || 0,
          averageBooking: completedBookings > 0 ? (monthlyRevenue[0]?.total || 0) / completedBookings : 0
        },
        recentBookings,
        topVehicles
      };

    } else if (req.user.role === 'customer') {
      // Customer dashboard
      const customerId = req.user._id;

      const [
        totalBookings,
        activeBookings,
        completedBookings,
        totalSpent,
        recentBookings,
        favoriteVehicles
      ] = await Promise.all([
        Booking.countDocuments({ customer: customerId }),
        Booking.countDocuments({ customer: customerId, status: 'active' }),
        Booking.countDocuments({ customer: customerId, status: 'completed' }),
        Booking.aggregate([
          {
            $match: {
              customer: new mongoose.Types.ObjectId(customerId),
              status: 'completed'
            }
          },
          { $group: { _id: null, total: { $sum: '$pricing.totalAmount' } } }
        ]),
        Booking.find({ customer: customerId })
          .populate('vehicle', 'make model images')
          .populate('company', 'name')
          .sort({ createdAt: -1 })
          .limit(5),
        Booking.aggregate([
          {
            $match: {
              customer: new mongoose.Types.ObjectId(customerId),
              status: 'completed'
            }
          },
          {
            $group: {
              _id: '$vehicle',
              bookings: { $sum: 1 }
            }
          },
          { $sort: { bookings: -1 } },
          { $limit: 3 },
          {
            $lookup: {
              from: 'vehicles',
              localField: '_id',
              foreignField: '_id',
              as: 'vehicle'
            }
          },
          { $unwind: '$vehicle' }
        ])
      ]);

      stats = {
        bookings: {
          total: totalBookings,
          active: activeBookings,
          completed: completedBookings
        },
        spending: {
          total: totalSpent[0]?.total || 0,
          average: completedBookings > 0 ? (totalSpent[0]?.total || 0) / completedBookings : 0
        },
        recentBookings,
        favoriteVehicles
      };
    }

    res.status(200).json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Dashboard overview error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get revenue analytics
// @route   GET /api/dashboard/revenue
// @access  Private (Company Admin, Employee, Super Admin)
router.get('/revenue', protect, authorize('company_admin', 'employee', 'super_admin'), async (req, res) => {
  try {
    const { period = '30d', companyId } = req.query;
    
    // Calculate date range
    let startDate;
    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Build match query
    let matchQuery = {
      status: 'completed',
      createdAt: { $gte: startDate }
    };

    // Add company filter
    if (req.user.role === 'company_admin' || req.user.role === 'employee') {
      matchQuery.company = req.user.company._id;
    } else if (req.user.role === 'super_admin' && companyId) {
      matchQuery.company = new mongoose.Types.ObjectId(companyId);
    }

    // Get daily revenue data
    const dailyRevenue = await Booking.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          revenue: { $sum: '$pricing.totalAmount' },
          bookings: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Get revenue by vehicle category
    const categoryRevenue = await Booking.aggregate([
      { $match: matchQuery },
      {
        $lookup: {
          from: 'vehicles',
          localField: 'vehicle',
          foreignField: '_id',
          as: 'vehicleData'
        }
      },
      { $unwind: '$vehicleData' },
      {
        $group: {
          _id: '$vehicleData.category',
          revenue: { $sum: '$pricing.totalAmount' },
          bookings: { $sum: 1 }
        }
      },
      { $sort: { revenue: -1 } }
    ]);

    // Get top performing vehicles
    const topVehicles = await Booking.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$vehicle',
          revenue: { $sum: '$pricing.totalAmount' },
          bookings: { $sum: 1 }
        }
      },
      { $sort: { revenue: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'vehicles',
          localField: '_id',
          foreignField: '_id',
          as: 'vehicle'
        }
      },
      { $unwind: '$vehicle' }
    ]);

    // Calculate totals
    const totals = await Booking.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$pricing.totalAmount' },
          totalBookings: { $sum: 1 },
          averageBookingValue: { $avg: '$pricing.totalAmount' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        period,
        totals: totals[0] || { totalRevenue: 0, totalBookings: 0, averageBookingValue: 0 },
        dailyRevenue,
        categoryRevenue,
        topVehicles
      }
    });

  } catch (error) {
    console.error('Revenue analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get booking analytics
// @route   GET /api/dashboard/bookings
// @access  Private (Company Admin, Employee, Super Admin)
router.get('/bookings', protect, authorize('company_admin', 'employee', 'super_admin'), async (req, res) => {
  try {
    const { period = '30d', companyId } = req.query;
    
    // Calculate date range
    let startDate;
    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Build match query
    let matchQuery = { createdAt: { $gte: startDate } };

    if (req.user.role === 'company_admin' || req.user.role === 'employee') {
      matchQuery.company = req.user.company._id;
    } else if (req.user.role === 'super_admin' && companyId) {
      matchQuery.company = new mongoose.Types.ObjectId(companyId);
    }

    // Get booking status distribution
    const statusDistribution = await Booking.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get daily booking counts
    const dailyBookings = await Booking.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Get booking duration analysis
    const durationAnalysis = await Booking.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: {
            $switch: {
              branches: [
                { case: { $lte: ['$duration.days', 1] }, then: '1 day' },
                { case: { $lte: ['$duration.days', 3] }, then: '2-3 days' },
                { case: { $lte: ['$duration.days', 7] }, then: '4-7 days' },
                { case: { $lte: ['$duration.days', 30] }, then: '1-4 weeks' }
              ],
              default: '1+ month'
            }
          },
          count: { $sum: 1 },
          averageRevenue: { $avg: '$pricing.totalAmount' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        period,
        statusDistribution,
        dailyBookings,
        durationAnalysis
      }
    });

  } catch (error) {
    console.error('Booking analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get fleet utilization
// @route   GET /api/dashboard/fleet
// @access  Private (Company Admin, Employee, Super Admin)
router.get('/fleet', protect, authorize('company_admin', 'employee', 'super_admin'), async (req, res) => {
  try {
    const { companyId } = req.query;
    
    // Build match query
    let matchQuery = { isActive: true };

    if (req.user.role === 'company_admin' || req.user.role === 'employee') {
      matchQuery.company = req.user.company._id;
    } else if (req.user.role === 'super_admin' && companyId) {
      matchQuery.company = new mongoose.Types.ObjectId(companyId);
    }

    // Get vehicle status distribution
    const statusDistribution = await Vehicle.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get category distribution
    const categoryDistribution = await Vehicle.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          averageRating: { $avg: '$averageRating' }
        }
      }
    ]);

    // Get utilization by vehicle
    const vehicleUtilization = await Vehicle.aggregate([
      { $match: matchQuery },
      {
        $lookup: {
          from: 'bookings',
          let: { vehicleId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$vehicle', '$$vehicleId'] },
                status: { $in: ['completed', 'active'] },
                createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
              }
            }
          ],
          as: 'bookings'
        }
      },
      {
        $project: {
          make: 1,
          model: 1,
          year: 1,
          licensePlate: 1,
          status: 1,
          category: 1,
          bookingCount: { $size: '$bookings' },
          totalRevenue: {
            $sum: '$bookings.pricing.totalAmount'
          }
        }
      },
      { $sort: { bookingCount: -1 } }
    ]);

    res.status(200).json({
      success: true,
      data: {
        statusDistribution,
        categoryDistribution,
        vehicleUtilization
      }
    });

  } catch (error) {
    console.error('Fleet analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
