const mongoose = require('mongoose');

const bookingSchema = new mongoose.Schema({
  // Reference Information
  bookingNumber: {
    type: String,
    unique: true,
    required: true
  },
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company',
    required: [true, 'Company is required']
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required']
  },
  vehicle: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vehicle',
    required: [true, 'Vehicle is required']
  },
  
  // Booking Details
  pickupLocation: {
    branch: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    address: {
      type: String,
      required: true
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  dropoffLocation: {
    branch: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    address: {
      type: String,
      required: true
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  
  // Timing
  pickupDateTime: {
    type: Date,
    required: [true, 'Pickup date and time is required']
  },
  dropoffDateTime: {
    type: Date,
    required: [true, 'Dropoff date and time is required']
  },
  actualPickupDateTime: Date,
  actualDropoffDateTime: Date,
  
  // Duration (calculated)
  duration: {
    hours: Number,
    days: Number
  },
  
  // Status
  status: {
    type: String,
    enum: [
      'pending',      // Waiting for approval
      'confirmed',    // Approved and confirmed
      'active',       // Vehicle picked up
      'completed',    // Vehicle returned
      'cancelled',    // Cancelled by customer or company
      'no_show'       // Customer didn't show up
    ],
    default: 'pending'
  },
  
  // Pricing
  pricing: {
    baseRate: {
      type: Number,
      required: true
    },
    rateType: {
      type: String,
      enum: ['hourly', 'daily', 'weekly', 'monthly'],
      required: true
    },
    subtotal: {
      type: Number,
      required: true
    },
    taxes: {
      type: Number,
      default: 0
    },
    fees: [{
      name: String,
      amount: Number,
      description: String
    }],
    discounts: [{
      name: String,
      amount: Number,
      type: {
        type: String,
        enum: ['percentage', 'fixed']
      }
    }],
    deposit: {
      type: Number,
      required: true
    },
    totalAmount: {
      type: Number,
      required: true
    }
  },
  
  // Payment Information
  payment: {
    method: {
      type: String,
      enum: ['cash', 'card', 'bank_transfer', 'online'],
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'paid', 'partial', 'refunded', 'failed'],
      default: 'pending'
    },
    paidAmount: {
      type: Number,
      default: 0
    },
    remainingAmount: {
      type: Number,
      default: 0
    },
    transactionId: String,
    paymentDate: Date,
    refundAmount: {
      type: Number,
      default: 0
    },
    refundDate: Date
  },
  
  // Driver Information
  primaryDriver: {
    name: {
      type: String,
      required: true
    },
    license: {
      number: {
        type: String,
        required: true
      },
      expiryDate: {
        type: Date,
        required: true
      },
      country: {
        type: String,
        required: true
      }
    },
    phone: {
      type: String,
      required: true
    },
    email: String
  },
  additionalDrivers: [{
    name: {
      type: String,
      required: true
    },
    license: {
      number: {
        type: String,
        required: true
      },
      expiryDate: {
        type: Date,
        required: true
      },
      country: {
        type: String,
        required: true
      }
    },
    phone: String,
    email: String,
    fee: {
      type: Number,
      default: 0
    }
  }],
  
  // Vehicle Condition
  vehicleCondition: {
    pickup: {
      mileage: Number,
      fuelLevel: {
        type: Number,
        min: 0,
        max: 100
      },
      damages: [{
        description: String,
        severity: {
          type: String,
          enum: ['minor', 'moderate', 'major']
        },
        images: [String]
      }],
      photos: [String],
      inspectedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      inspectionDate: Date,
      notes: String
    },
    dropoff: {
      mileage: Number,
      fuelLevel: {
        type: Number,
        min: 0,
        max: 100
      },
      damages: [{
        description: String,
        severity: {
          type: String,
          enum: ['minor', 'moderate', 'major']
        },
        images: [String],
        cost: Number
      }],
      photos: [String],
      inspectedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      inspectionDate: Date,
      notes: String,
      additionalCharges: [{
        description: String,
        amount: Number,
        reason: String
      }]
    }
  },
  
  // Insurance and Protection
  insurance: {
    type: {
      type: String,
      enum: ['basic', 'comprehensive', 'premium'],
      default: 'basic'
    },
    cost: {
      type: Number,
      default: 0
    },
    deductible: {
      type: Number,
      default: 0
    }
  },
  
  // Special Requests and Add-ons
  addOns: [{
    name: String,
    description: String,
    cost: Number,
    quantity: {
      type: Number,
      default: 1
    }
  }],
  specialRequests: String,
  
  // Communication
  notes: [{
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    message: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    isInternal: {
      type: Boolean,
      default: false
    }
  }],
  
  // Review and Rating
  review: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    reviewDate: Date
  },
  
  // Tracking
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Cancellation
  cancellation: {
    reason: String,
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    cancelledAt: Date,
    refundAmount: Number
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for total duration in hours
bookingSchema.virtual('totalHours').get(function() {
  if (this.pickupDateTime && this.dropoffDateTime) {
    return Math.ceil((this.dropoffDateTime - this.pickupDateTime) / (1000 * 60 * 60));
  }
  return 0;
});

// Virtual for total days
bookingSchema.virtual('totalDays').get(function() {
  return Math.ceil(this.totalHours / 24);
});

// Indexes for better performance
bookingSchema.index({ bookingNumber: 1 });
bookingSchema.index({ company: 1, status: 1 });
bookingSchema.index({ customer: 1 });
bookingSchema.index({ vehicle: 1 });
bookingSchema.index({ pickupDateTime: 1, dropoffDateTime: 1 });
bookingSchema.index({ status: 1, pickupDateTime: 1 });

// Pre-save middleware to generate booking number
bookingSchema.pre('save', async function(next) {
  if (this.isNew) {
    const count = await this.constructor.countDocuments();
    this.bookingNumber = `BK${Date.now().toString().slice(-6)}${(count + 1).toString().padStart(4, '0')}`;
    
    // Calculate duration
    if (this.pickupDateTime && this.dropoffDateTime) {
      const diffMs = this.dropoffDateTime - this.pickupDateTime;
      this.duration = {
        hours: Math.ceil(diffMs / (1000 * 60 * 60)),
        days: Math.ceil(diffMs / (1000 * 60 * 60 * 24))
      };
    }
    
    // Calculate remaining amount
    this.payment.remainingAmount = this.pricing.totalAmount - this.payment.paidAmount;
  }
  next();
});

module.exports = mongoose.model('Booking', bookingSchema);
