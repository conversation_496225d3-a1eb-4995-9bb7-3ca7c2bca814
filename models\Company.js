const mongoose = require('mongoose');

const branchSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Branch name is required'],
    trim: true
  },
  address: {
    street: {
      type: String,
      required: [true, 'Street address is required']
    },
    city: {
      type: String,
      required: [true, 'City is required']
    },
    state: String,
    country: {
      type: String,
      required: [true, 'Country is required']
    },
    zipCode: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required']
  },
  email: String,
  workingHours: {
    monday: { open: String, close: String, closed: { type: Boolean, default: false } },
    tuesday: { open: String, close: String, closed: { type: Boolean, default: false } },
    wednesday: { open: String, close: String, closed: { type: Boolean, default: false } },
    thursday: { open: String, close: String, closed: { type: Boolean, default: false } },
    friday: { open: String, close: String, closed: { type: Boolean, default: false } },
    saturday: { open: String, close: String, closed: { type: Boolean, default: false } },
    sunday: { open: String, close: String, closed: { type: Boolean, default: true } }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, { timestamps: true });

const companySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Company name is required'],
    trim: true,
    maxlength: [100, 'Company name cannot exceed 100 characters']
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  logo: {
    type: String,
    default: null
  },
  businessLicense: {
    number: {
      type: String,
      required: [true, 'Business license number is required']
    },
    expiryDate: {
      type: Date,
      required: [true, 'Business license expiry date is required']
    },
    document: String // URL to uploaded document
  },
  taxId: {
    type: String,
    required: [true, 'Tax ID is required']
  },
  contactInfo: {
    email: {
      type: String,
      required: [true, 'Company email is required'],
      lowercase: true
    },
    phone: {
      type: String,
      required: [true, 'Company phone is required']
    },
    website: String,
    socialMedia: {
      facebook: String,
      twitter: String,
      instagram: String,
      linkedin: String
    }
  },
  headquarters: {
    street: {
      type: String,
      required: [true, 'Headquarters street address is required']
    },
    city: {
      type: String,
      required: [true, 'Headquarters city is required']
    },
    state: String,
    country: {
      type: String,
      required: [true, 'Headquarters country is required']
    },
    zipCode: String
  },
  branches: [branchSchema],
  subscription: {
    plan: {
      type: String,
      enum: ['basic', 'premium', 'enterprise'],
      default: 'basic'
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'trial'],
      default: 'trial'
    },
    startDate: {
      type: Date,
      default: Date.now
    },
    endDate: Date,
    maxVehicles: {
      type: Number,
      default: 10
    },
    maxUsers: {
      type: Number,
      default: 5
    }
  },
  settings: {
    currency: {
      type: String,
      enum: ['USD', 'EUR', 'MAD', 'SAR'],
      default: 'USD'
    },
    timezone: {
      type: String,
      default: 'UTC'
    },
    language: {
      type: String,
      enum: ['en', 'ar', 'fr'],
      default: 'en'
    },
    bookingSettings: {
      requireApproval: {
        type: Boolean,
        default: false
      },
      minBookingDuration: {
        type: Number,
        default: 1 // hours
      },
      maxBookingDuration: {
        type: Number,
        default: 720 // hours (30 days)
      },
      advanceBookingLimit: {
        type: Number,
        default: 90 // days
      }
    },
    paymentSettings: {
      acceptCash: {
        type: Boolean,
        default: true
      },
      acceptCard: {
        type: Boolean,
        default: true
      },
      requireDeposit: {
        type: Boolean,
        default: true
      },
      depositAmount: {
        type: Number,
        default: 200
      }
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  verificationDate: Date,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for total vehicles
companySchema.virtual('totalVehicles', {
  ref: 'Vehicle',
  localField: '_id',
  foreignField: 'company',
  count: true
});

// Virtual for total bookings
companySchema.virtual('totalBookings', {
  ref: 'Booking',
  localField: '_id',
  foreignField: 'company',
  count: true
});

// Index for better performance
companySchema.index({ slug: 1 });
companySchema.index({ 'subscription.status': 1 });
companySchema.index({ isActive: 1, isVerified: 1 });

// Pre-save middleware to generate slug
companySchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-zA-Z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }
  next();
});

module.exports = mongoose.model('Company', companySchema);
