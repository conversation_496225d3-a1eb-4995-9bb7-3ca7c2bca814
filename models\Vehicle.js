const mongoose = require('mongoose');

const maintenanceRecordSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['routine', 'repair', 'inspection', 'cleaning'],
    required: true
  },
  description: {
    type: String,
    required: true
  },
  cost: {
    type: Number,
    required: true
  },
  performedBy: String,
  performedAt: {
    type: Date,
    default: Date.now
  },
  nextMaintenanceDate: Date,
  documents: [String] // URLs to maintenance documents
});

const vehicleSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company',
    required: [true, 'Company is required']
  },
  branch: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, 'Branch is required']
  },
  // Basic Information
  make: {
    type: String,
    required: [true, 'Vehicle make is required'],
    trim: true
  },
  model: {
    type: String,
    required: [true, 'Vehicle model is required'],
    trim: true
  },
  year: {
    type: Number,
    required: [true, 'Vehicle year is required'],
    min: [1990, 'Year must be 1990 or later'],
    max: [new Date().getFullYear() + 1, 'Year cannot be in the future']
  },
  color: {
    type: String,
    required: [true, 'Vehicle color is required']
  },
  licensePlate: {
    type: String,
    required: [true, 'License plate is required'],
    unique: true,
    uppercase: true
  },
  vin: {
    type: String,
    required: [true, 'VIN is required'],
    unique: true,
    uppercase: true,
    minlength: [17, 'VIN must be 17 characters'],
    maxlength: [17, 'VIN must be 17 characters']
  },
  
  // Classification
  category: {
    type: String,
    enum: ['economy', 'compact', 'midsize', 'fullsize', 'luxury', 'suv', 'van', 'truck'],
    required: [true, 'Vehicle category is required']
  },
  type: {
    type: String,
    enum: ['sedan', 'hatchback', 'coupe', 'convertible', 'suv', 'pickup', 'van', 'minivan'],
    required: [true, 'Vehicle type is required']
  },
  
  // Specifications
  specifications: {
    engine: {
      type: String,
      required: true
    },
    transmission: {
      type: String,
      enum: ['manual', 'automatic', 'cvt'],
      required: true
    },
    fuelType: {
      type: String,
      enum: ['gasoline', 'diesel', 'hybrid', 'electric'],
      required: true
    },
    fuelCapacity: Number, // in liters
    mileage: Number, // km per liter
    seatingCapacity: {
      type: Number,
      required: true,
      min: 1,
      max: 15
    },
    doors: {
      type: Number,
      required: true,
      min: 2,
      max: 5
    },
    airConditioning: {
      type: Boolean,
      default: true
    },
    gps: {
      type: Boolean,
      default: false
    }
  },
  
  // Features
  features: [{
    type: String,
    enum: [
      'bluetooth', 'usb_ports', 'wireless_charging', 'backup_camera',
      'parking_sensors', 'cruise_control', 'leather_seats', 'sunroof',
      'heated_seats', 'premium_audio', 'navigation', 'keyless_entry',
      'push_start', 'lane_assist', 'collision_warning', 'blind_spot_monitoring'
    ]
  }],
  
  // Pricing
  pricing: {
    hourly: {
      type: Number,
      required: true,
      min: 0
    },
    daily: {
      type: Number,
      required: true,
      min: 0
    },
    weekly: {
      type: Number,
      min: 0
    },
    monthly: {
      type: Number,
      min: 0
    },
    deposit: {
      type: Number,
      required: true,
      min: 0
    }
  },
  
  // Status and Availability
  status: {
    type: String,
    enum: ['available', 'rented', 'maintenance', 'out_of_service'],
    default: 'available'
  },
  currentMileage: {
    type: Number,
    required: true,
    min: 0
  },
  lastServiceMileage: {
    type: Number,
    default: 0
  },
  nextServiceMileage: Number,
  
  // Insurance and Registration
  insurance: {
    provider: {
      type: String,
      required: true
    },
    policyNumber: {
      type: String,
      required: true
    },
    expiryDate: {
      type: Date,
      required: true
    },
    coverage: {
      type: String,
      enum: ['basic', 'comprehensive', 'full'],
      required: true
    }
  },
  registration: {
    expiryDate: {
      type: Date,
      required: true
    },
    document: String // URL to registration document
  },
  
  // Media
  images: [{
    url: {
      type: String,
      required: true
    },
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  
  // Maintenance
  maintenanceRecords: [maintenanceRecordSchema],
  lastMaintenanceDate: Date,
  nextMaintenanceDate: Date,
  
  // Tracking
  currentLocation: {
    latitude: Number,
    longitude: Number,
    address: String,
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  
  // Ratings and Reviews
  averageRating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  totalReviews: {
    type: Number,
    default: 0
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for vehicle display name
vehicleSchema.virtual('displayName').get(function() {
  return `${this.year} ${this.make} ${this.model}`;
});

// Virtual for primary image
vehicleSchema.virtual('primaryImage').get(function() {
  const primary = this.images.find(img => img.isPrimary);
  return primary ? primary.url : (this.images.length > 0 ? this.images[0].url : null);
});

// Indexes for better performance
vehicleSchema.index({ company: 1, status: 1 });
vehicleSchema.index({ category: 1, type: 1 });
vehicleSchema.index({ licensePlate: 1 });
vehicleSchema.index({ vin: 1 });
vehicleSchema.index({ 'pricing.daily': 1 });

// Pre-save middleware
vehicleSchema.pre('save', function(next) {
  // Ensure only one primary image
  if (this.images && this.images.length > 0) {
    const primaryImages = this.images.filter(img => img.isPrimary);
    if (primaryImages.length > 1) {
      this.images.forEach((img, index) => {
        img.isPrimary = index === 0;
      });
    } else if (primaryImages.length === 0) {
      this.images[0].isPrimary = true;
    }
  }
  next();
});

module.exports = mongoose.model('Vehicle', vehicleSchema);
