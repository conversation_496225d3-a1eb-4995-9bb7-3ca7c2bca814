import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';
import { 
  LoginCredentials, 
  RegisterData, 
  AuthResponse, 
  ChangePasswordData,
  User 
} from '../types/auth';
import { 
  ApiResponse, 
  PaginationParams, 
  Company, 
  Vehicle, 
  Booking, 
  DashboardStats 
} from '../types';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
      toast.error('Session expired. Please login again.');
    } else if (error.response?.status === 403) {
      toast.error('You do not have permission to perform this action.');
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials: LoginCredentials): Promise<AxiosResponse<AuthResponse>> =>
    api.post('/auth/login', credentials),
  
  register: (data: RegisterData): Promise<AxiosResponse<AuthResponse>> =>
    api.post('/auth/register', data),
  
  getMe: (): Promise<AxiosResponse<ApiResponse<User>>> =>
    api.get('/auth/me'),
  
  updateProfile: (data: Partial<User>): Promise<AxiosResponse<ApiResponse<User>>> =>
    api.put('/auth/profile', data),
  
  changePassword: (data: ChangePasswordData): Promise<AxiosResponse<ApiResponse>> =>
    api.put('/auth/change-password', data),
  
  logout: (): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/logout'),
};

// Companies API
export const companiesAPI = {
  getAll: (params?: PaginationParams): Promise<AxiosResponse<ApiResponse<{ companies: Company[] }>>> =>
    api.get('/companies', { params }),
  
  getById: (id: string): Promise<AxiosResponse<ApiResponse<Company>>> =>
    api.get(`/companies/${id}`),
  
  update: (id: string, data: Partial<Company>): Promise<AxiosResponse<ApiResponse<Company>>> =>
    api.put(`/companies/${id}`, data),
  
  addBranch: (id: string, data: any): Promise<AxiosResponse<ApiResponse<Company>>> =>
    api.post(`/companies/${id}/branches`, data),
  
  updateBranch: (id: string, branchId: string, data: any): Promise<AxiosResponse<ApiResponse<Company>>> =>
    api.put(`/companies/${id}/branches/${branchId}`, data),
  
  deleteBranch: (id: string, branchId: string): Promise<AxiosResponse<ApiResponse<Company>>> =>
    api.delete(`/companies/${id}/branches/${branchId}`),
  
  getStats: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get(`/companies/${id}/stats`),
};

// Vehicles API
export const vehiclesAPI = {
  getAll: (params?: any): Promise<AxiosResponse<ApiResponse<{ vehicles: Vehicle[] }>>> =>
    api.get('/vehicles', { params }),
  
  getById: (id: string): Promise<AxiosResponse<ApiResponse<{ vehicle: Vehicle; bookings: any[]; similarVehicles: Vehicle[] }>>> =>
    api.get(`/vehicles/${id}`),
  
  create: (data: Partial<Vehicle>): Promise<AxiosResponse<ApiResponse<Vehicle>>> =>
    api.post('/vehicles', data),
  
  update: (id: string, data: Partial<Vehicle>): Promise<AxiosResponse<ApiResponse<Vehicle>>> =>
    api.put(`/vehicles/${id}`, data),
  
  delete: (id: string): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/vehicles/${id}`),
};

// Bookings API
export const bookingsAPI = {
  getAll: (params?: any): Promise<AxiosResponse<ApiResponse<{ bookings: Booking[] }>>> =>
    api.get('/bookings', { params }),
  
  getById: (id: string): Promise<AxiosResponse<ApiResponse<Booking>>> =>
    api.get(`/bookings/${id}`),
  
  create: (data: any): Promise<AxiosResponse<ApiResponse<Booking>>> =>
    api.post('/bookings', data),
  
  updateStatus: (id: string, data: { status: string; reason?: string; vehicleCondition?: any }): Promise<AxiosResponse<ApiResponse<Booking>>> =>
    api.put(`/bookings/${id}/status`, data),
  
  addNote: (id: string, data: { message: string; isInternal?: boolean }): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post(`/bookings/${id}/notes`, data),
};

// Users API
export const usersAPI = {
  getAll: (params?: PaginationParams): Promise<AxiosResponse<ApiResponse<{ users: User[] }>>> =>
    api.get('/users', { params }),
  
  getById: (id: string): Promise<AxiosResponse<ApiResponse<User>>> =>
    api.get(`/users/${id}`),
  
  create: (data: Partial<User>): Promise<AxiosResponse<ApiResponse<User>>> =>
    api.post('/users', data),
  
  update: (id: string, data: Partial<User>): Promise<AxiosResponse<ApiResponse<User>>> =>
    api.put(`/users/${id}`, data),
  
  delete: (id: string): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/users/${id}`),
  
  getStats: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get(`/users/${id}/stats`),
};

// Payments API
export const paymentsAPI = {
  createIntent: (data: { bookingId: string; amount: number }): Promise<AxiosResponse<ApiResponse<{ clientSecret: string; paymentIntentId: string }>>> =>
    api.post('/payments/create-intent', data),
  
  confirmPayment: (data: { paymentIntentId: string; bookingId: string }): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/payments/confirm', data),
  
  processCashPayment: (data: { bookingId: string; amount: number; notes?: string }): Promise<AxiosResponse<ApiResponse<Booking>>> =>
    api.post('/payments/cash', data),
  
  processRefund: (data: { bookingId: string; amount: number; reason: string }): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/payments/refund', data),
  
  getPaymentHistory: (bookingId: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get(`/payments/booking/${bookingId}/history`),
};

// Dashboard API
export const dashboardAPI = {
  getOverview: (): Promise<AxiosResponse<ApiResponse<DashboardStats>>> =>
    api.get('/dashboard/overview'),
  
  getRevenue: (params?: { period?: string; companyId?: string }): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/dashboard/revenue', { params }),
  
  getBookings: (params?: { period?: string; companyId?: string }): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/dashboard/bookings', { params }),
  
  getFleet: (params?: { companyId?: string }): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/dashboard/fleet', { params }),
};

// Health check API
export const healthAPI = {
  check: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/health'),
  
  test: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/test'),
};

// Mock data API (for development)
export const mockAPI = {
  getCompanies: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/mock/companies'),
  
  getVehicles: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/mock/vehicles'),
  
  getDashboard: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/mock/dashboard'),
};

export default api;
